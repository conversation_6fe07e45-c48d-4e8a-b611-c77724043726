"""
清洗界面组件
"""
import os
import logging
from datetime import datetime
from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                             QGroupBox, QCheckBox, QGridLayout, QTextEdit, 
                             QProgressBar, QFileDialog, QMessageBox)
from PyQt5.QtGui import QFont
from utils.config import CLEANING_OPERATIONS, LOGS_DIR

class CleanerInterface:
    def __init__(self, parent):
        self.parent = parent
        self.operation_checkboxes = {}
        
    def create_interface(self, layout):
        """创建清洗界面"""
        # 添加标题
        title_label = QLabel("数据清洗")
        title_label.setFont(QFont("Roboto", 14, QFont.Bold))
        title_label.setStyleSheet("color: #E0E0E0;")
        layout.addWidget(title_label)

        # 文件夹选择组
        folder_group = QGroupBox("文件夹选择")
        folder_layout = QHBoxLayout()
        # 设置初始值为数据存储路径的当前值
        self.folder_path_label = QLabel(self.parent.download_interface.local_data_path_edit.text())
        self.browse_button = QPushButton("浏览...")
        self.browse_button.clicked.connect(self.browse_folder)
        folder_layout.addWidget(self.folder_path_label)
        folder_layout.addWidget(self.browse_button)
        folder_group.setLayout(folder_layout)
        layout.addWidget(folder_group)

        # 清洗操作组
        operations_group = QGroupBox("清洗操作")
        operations_layout = QGridLayout()
        self.operation_checkboxes = {}
        
        for i, (op, label) in enumerate(CLEANING_OPERATIONS):
            checkbox = QCheckBox(label)
            # 除了 remove_outliers 之外的选项默认勾选
            checkbox.setChecked(op != 'remove_outliers')
            self.operation_checkboxes[op] = checkbox
            operations_layout.addWidget(checkbox, i // 2, i % 2)
        operations_group.setLayout(operations_layout)
        layout.addWidget(operations_group)

        # 清洗按钮和进度条
        clean_layout = QVBoxLayout()
        self.clean_button = QPushButton("开始清洗")
        self.clean_button.clicked.connect(self.start_cleaning)
        self.file_progress_bar = QProgressBar()
        self.total_progress_bar = QProgressBar()
        clean_layout.addWidget(self.clean_button)
        clean_layout.addWidget(QLabel("当前文件进度:"))
        clean_layout.addWidget(self.file_progress_bar)
        clean_layout.addWidget(QLabel("总体进度:"))
        clean_layout.addWidget(self.total_progress_bar)
        layout.addLayout(clean_layout)

        # 预览区
        preview_group = QGroupBox("清洗结果预览")
        preview_group.setObjectName("预览组")
        preview_layout = QVBoxLayout()
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        preview_layout.addWidget(self.preview_text)

        # 添加保存日志按钮
        save_layout = QHBoxLayout()
        save_layout.addStretch()
        self.save_log_button = QPushButton("保存清洗日志")
        self.save_log_button.clicked.connect(self.save_cleaning_log)
        save_layout.addWidget(self.save_log_button)
        preview_layout.addLayout(save_layout)

        preview_group.setLayout(preview_layout)
        layout.addWidget(preview_group)

    def browse_folder(self):
        """浏览文件夹并更新显示"""
        folder_path = QFileDialog.getExistingDirectory(self.parent, "选择数据文件夹")
        if folder_path:
            self.folder_path_label.setText(folder_path)
            self.load_folder_info(folder_path)
            # 同时更新下载界面的路径
            if hasattr(self.parent, 'download_interface'):
                self.parent.download_interface.local_data_path_edit.setText(folder_path)

    def load_folder_info(self, folder_path):
        """更新文件夹信息加载方法"""
        try:
            if not os.path.exists(folder_path):
                self.preview_text.setText("文件夹不存在")
                return

            csv_files = [f for f in os.listdir(folder_path) if f.endswith('.csv')]
            
            if not csv_files:
                self.preview_text.setText("文件夹中没有CSV文件")
                return

            total_size = sum(os.path.getsize(os.path.join(folder_path, f)) for f in csv_files)
            info_text = f"文件夹信息更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            info_text += f"文件夹路径: {folder_path}\n"
            info_text += f"CSV文件数量: {len(csv_files)}\n"
            info_text += f"总文件大小: {total_size / (1024*1024):.2f} MB\n\n"
            info_text += "文件列表:\n"
            
            for file in sorted(csv_files):
                file_path = os.path.join(folder_path, file)
                file_size = os.path.getsize(file_path)
                file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                info_text += f"- {file}\n"
                info_text += f"  大小: {file_size / 1024:.1f} KB\n"
                info_text += f"  修改时间: {file_time.strftime('%Y-%m-%d %H:%M:%S')}\n"

            self.preview_text.setText(info_text)
            logging.info(f"成功加载文件夹信息: {folder_path}")
        except Exception as e:
            error_msg = f"加载文件夹信息时出错: {str(e)}"
            logging.error(error_msg)
            QMessageBox.warning(self.parent, "加载错误", error_msg)

    def start_cleaning(self):
        """开始清洗数据"""
        folder_path = self.folder_path_label.text()
        if folder_path == "未选择文件夹":
            return

        # 显示警告对话框
        reply = QMessageBox.warning(
            self.parent,
            "警告",
            "清洗后数据将覆盖原始数据，是否继续？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No  # 默认选择No按钮
        )
        
        if reply == QMessageBox.No:
            return

        operations = [op for op, checkbox in self.operation_checkboxes.items() if checkbox.isChecked()]
        
        # 导入清洗线程和数据清洗器
        from components.cleaner_thread import CleanerThread
        from components.data_cleaner import StockDataCleaner
        
        cleaner = StockDataCleaner()
        self.cleaner_thread = CleanerThread(cleaner, folder_path, operations)
        self.cleaner_thread.progress_updated.connect(self.update_cleaner_progress)
        self.cleaner_thread.cleaning_completed.connect(self.show_cleaning_preview)
        self.cleaner_thread.error_occurred.connect(self.show_cleaner_error)
        self.cleaner_thread.start()

        self.clean_button.setEnabled(False)
        self.browse_button.setEnabled(False)

    def update_cleaner_progress(self, file_progress, total_progress):
        """更新清洗进度"""
        self.file_progress_bar.setValue(file_progress)
        self.total_progress_bar.setValue(total_progress)

    def show_cleaning_preview(self, cleaning_info):
        """Generate and display the cleaning preview with proper handling of deleted rows"""
        import pandas as pd
        
        preview_text = "清洗完成总览报告\n" + "="*50 + "\n\n"
        
        # 总概况
        total_rows_before = sum(info['before']['shape'][0] for info in cleaning_info.values())
        total_rows_after = sum(info['after']['shape'][0] for info in cleaning_info.values())
        preview_text += f"处理文件总数: {len(cleaning_info)}\n"
        preview_text += f"总行数变化: {total_rows_before} -> {total_rows_after} (清理: {total_rows_before - total_rows_after} 行)\n\n"
        
        # 按操作类统计总删除行数
        operation_totals = {}
        for file_info in cleaning_info.values():
            for op, count in file_info['after']['row_changes'].items():
                operation_totals[op] = operation_totals.get(op, 0) + count
        
        preview_text += "各类型数据清理统计:\n" + "-"*30 + "\n"
        for op, total in operation_totals.items():
            op_name = {
                'remove_duplicates': '重复数据',
                'handle_missing_values': '缺失值',
                'remove_outliers': '异常值',
                'handle_non_trading_hours': '非交易时间数据'
            }.get(op, op)
            preview_text += f"{op_name}: 共清理 {total} 行\n"
        preview_text += "\n"

        # 单个文件详细信息
        preview_text += "各文件详细清理报告\n" + "="*50 + "\n\n"
        
        for file, info in cleaning_info.items():
            preview_text += f"文件: {file}\n" + "-"*50 + "\n"
            preview_text += f"初始行数: {info['before']['shape'][0]}\n"
            preview_text += f"最终行数: {info['after']['shape'][0]}\n"
            preview_text += f"总清理行数: {info['before']['shape'][0] - info['after']['shape'][0]}\n\n"
            
            # 各操作的详细信息
            preview_text += "清理详情:\n"
            for op, count in info['after']['row_changes'].items():
                if count > 0:
                    op_name = {
                        'remove_duplicates': '重复数据',
                        'handle_missing_values': '缺失值',
                        'remove_outliers': '异常值',
                        'handle_non_trading_hours': '非交易时间数据'
                    }.get(op, op)
                    preview_text += f"\n>> {op_name}清理详情:\n"
                    preview_text += f"清理行数: {count}\n"
                    
                    # 检查被删除的行的详细信息
                    if op in info['after']['deleted_rows']:
                        deleted_data = info['after']['deleted_rows'][op]
                        
                        # 检查deleted_data是否为DataFrame
                        if isinstance(deleted_data, pd.DataFrame) and not deleted_data.empty:
                            preview_text += "删除的数据详情:\n"
                            
                            if op == 'remove_outliers':
                                # 对于异常值，显示所有数据并按照数值大小排序
                                preview_text += "异常值数据:\n"
                                numeric_cols = deleted_data.select_dtypes(include=['float64', 'int64']).columns
                                for col in numeric_cols:
                                    if col in ['open', 'high', 'low', 'close', 'volume']:
                                        preview_text += f"\n{col} 列的异常值分析:\n"
                                        preview_text += f"最小值: {deleted_data[col].min()}\n"
                                        preview_text += f"最大值: {deleted_data[col].max()}\n"
                                        preview_text += "所有异常数据(按照列排序):\n"
                                        sorted_data = deleted_data.sort_values(by=col)
                                        preview_text += sorted_data.to_string() + "\n"
                                        preview_text += "-"*30 + "\n"
                            else:
                                # 其他类型的删除显示所有行
                                preview_text += "所有删除的数据:\n"
                                preview_text += deleted_data.to_string() + "\n"
                                preview_text += "-"*30 + "\n"
                        elif isinstance(deleted_data, dict):
                            # 处理特殊情况，如时间重复数据
                            for key, data in deleted_data.items():
                                if isinstance(data, pd.DataFrame) and not data.empty:
                                    preview_text += f"\n{key}:\n"
                                    preview_text += data.to_string() + "\n"
                                    preview_text += "-"*30 + "\n"
            
            preview_text += "\n" + "="*50 + "\n\n"

        self.preview_text.setText(preview_text)
        self.clean_button.setEnabled(True)
        self.browse_button.setEnabled(True)

    def show_cleaner_error(self, error_message):
        """显示清洗错误"""
        QMessageBox.critical(self.parent, "清洗错误", f"数据清洗过程中出错: {error_message}")
        self.clean_button.setEnabled(True)
        self.browse_button.setEnabled(True)

    def save_cleaning_log(self):
        """保存清洗日志"""
        try:
            # 生成默认文件名
            default_filename = f"cleaning_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            default_path = os.path.join(LOGS_DIR, default_filename)
            
            # 获取保存路径，设置默认目录为logs目录
            file_path, _ = QFileDialog.getSaveFileName(
                self.parent,
                "保存清洗日志",
                default_path,
                "Text Files (*.txt);;All Files (*)"
            )
            
            if file_path:
                # 确保目标目录存在
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.preview_text.toPlainText())
                QMessageBox.information(self.parent, "成功", "清洗日志已保存！")
                
        except Exception as e:
            QMessageBox.critical(self.parent, "错误", f"保存日志时发生错误: {str(e)}")
