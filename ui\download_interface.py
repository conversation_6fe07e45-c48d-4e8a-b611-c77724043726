"""
下载界面组件
"""
import os
import logging
from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                             QGroupBox, QComboBox, QDateEdit, QTimeEdit, QCheckBox, 
                             QGridLayout, QTextEdit, QProgressBar, QFileDialog, 
                             QMessageBox)
from PyQt5.QtCore import QDate, QTime, QSettings, Qt
from PyQt5.QtGui import QFont
from utils.config import TICK_FIELDS, KLINE_FIELDS, STOCK_TYPES, get_data_directory

class DownloadInterface:
    def __init__(self, parent):
        self.parent = parent
        self.field_checkboxes = {}
        
    def create_interface(self, layout):
        """创建下载界面"""
        title_font = QFont("Roboto", 10, QFont.Bold)
        
        # 添加标题
        title_label = QLabel("数据下载")
        title_label.setFont(QFont("Roboto", 14, QFont.Bold))
        title_label.setStyleSheet("color: #E0E0E0;")
        layout.addWidget(title_label)

        self.add_path_group(layout, title_font)
        self.add_stock_group(layout, title_font)
        self.add_period_group(layout, title_font)
        self.add_field_group(layout, title_font)
        self.add_date_group(layout, title_font)
        self.add_time_group(layout, title_font)
        self.add_download_section(layout)

        self.status_label = QLabel()
        self.status_label.setStyleSheet("color: #E0E0E0; font-size: 24px;")
        layout.addWidget(self.status_label)

    def add_path_group(self, layout, title_font):
        """添加路径选择组"""
        path_group = QGroupBox("数据存储路径")
        path_group.setFont(title_font)
        path_layout = QHBoxLayout()
        
        # 从QSettings读取上次保存的路径，如果没有则使用默认路径
        settings = QSettings('KHQuant', 'StockAnalyzer')
        saved_path = settings.value('data_path', "D:/stock_data")
        logging.info(f"从QSettings读取保存的路径: {saved_path}")
        self.local_data_path_edit = QLineEdit(saved_path)
        
        # 添加信号连接，当文本改变时更新清洗界面的路径和保存设置
        self.local_data_path_edit.textChanged.connect(self.parent.update_folder_path_label)
        self.local_data_path_edit.editingFinished.connect(self.save_path_setting)
        path_layout.addWidget(self.local_data_path_edit)
        browse_button = QPushButton("浏览...")
        browse_button.clicked.connect(self.browse_path)
        path_layout.addWidget(browse_button)
        path_group.setLayout(path_layout)
        layout.addWidget(path_group)

    def save_path_setting(self):
        """保存当前路径到设置"""
        try:
            path = self.local_data_path_edit.text().strip()
            if path:
                settings = QSettings('KHQuant', 'StockAnalyzer')
                settings.setValue('data_path', path)
                settings.sync()  # 强制同步设置到磁盘
                logging.info(f"已保存路径到设置: {path}")
                
                # 如果清洗界面的文件夹路径标签存在，也更新它
                if hasattr(self.parent, 'folder_path_label'):
                    self.parent.folder_path_label.setText(path)
                self.parent.refresh_folder()  # 立即刷新文件夹内容
        except Exception as e:
            logging.error(f"保存路径设置时出错: {str(e)}")
            QMessageBox.warning(self.parent, "警告", f"保存路径设置时出错: {str(e)}")

    def browse_path(self):
        """更新浏览路径方法"""
        path = QFileDialog.getExistingDirectory(self.parent, "选择数据存储路径")
        if path:
            logging.info(f"用户选择了新路径: {path}")
            self.local_data_path_edit.setText(path)
            
            # 保存路径到QSettings
            try:
                settings = QSettings('KHQuant', 'StockAnalyzer')
                settings.setValue('data_path', path)
                settings.sync()  # 强制同步设置到磁盘
                
                # 验证设置是否保存成功
                saved_value = settings.value('data_path')
                logging.info(f"验证保存的路径: {saved_value}")
                
                if saved_value != path:
                    logging.error("路径保存验证失败！")
                else:
                    logging.info("路径已成功保存到QSettings")
                    
            except Exception as e:
                logging.error(f"保存路径到QSettings时出错: {str(e)}")
                QMessageBox.warning(self.parent, "警告", f"保存路径设置时出错: {str(e)}")
            
            # 如果清洗界面的文件夹路径标签存在，也更新它
            if hasattr(self.parent, 'folder_path_label'):
                self.parent.folder_path_label.setText(path)
                logging.info("已更新清洗界面的路径标签")
            
            self.parent.refresh_folder()  # 立即刷新文件夹内容
            logging.info("已刷新文件夹内容")

    def add_stock_group(self, layout, title_font):
        """添加股票选择组"""
        stock_group = QGroupBox("股票代码列表文件")
        stock_group.setFont(title_font)
        stock_layout = QVBoxLayout()
        
        # 添加复选框组
        self.stock_checkboxes = {}
        self.stock_files = {}
        
        # 创建复选框网格布局
        checkbox_layout = QGridLayout()
        row = 0
        col = 0
        for stock_type, display_name in STOCK_TYPES.items():
            if stock_type == 'custom':
                # 为自选清单创建特殊的标签和复选框布局
                custom_layout = QHBoxLayout()
                checkbox = QCheckBox()
                self.stock_checkboxes[stock_type] = checkbox
                custom_layout.addWidget(checkbox)
                
                # 创建可点击的标签
                custom_label = QLabel(display_name)
                custom_label.setStyleSheet("""
                    QLabel {
                        color: #3DAEE9;
                        text-decoration: underline;
                        cursor: pointer;
                    }
                    QLabel:hover {
                        color: #2980B9;
                    }
                """)
                custom_label.setCursor(Qt.PointingHandCursor)
                custom_label.mousePressEvent = self.parent.open_custom_list
                custom_layout.addWidget(custom_label)
                custom_layout.addStretch()
                
                # 将自选清单放在新的一行
                if col != 0:
                    row += 1
                    col = 0
                checkbox_layout.addLayout(custom_layout, row, col, 1, 3)
            else:
                checkbox = QCheckBox(display_name)
                self.stock_checkboxes[stock_type] = checkbox
                checkbox_layout.addWidget(checkbox, row, col)
                col += 1
                if col > 2:
                    col = 0
                    row += 1
        
        stock_layout.addLayout(checkbox_layout)
        
        # 自定义列表部分
        custom_group = QGroupBox("自定义列表")
        custom_layout = QHBoxLayout()
        
        browse_button = QPushButton("添加自定义列表")
        browse_button.clicked.connect(self.add_custom_stock_file)
        custom_layout.addWidget(browse_button)
        
        clear_button = QPushButton("清空列表")
        clear_button.clicked.connect(self.clear_stock_files)
        custom_layout.addWidget(clear_button)
        
        custom_layout.addStretch()
        
        custom_group.setLayout(custom_layout)
        stock_layout.addWidget(custom_group)
        
        # 添加已选文件预览
        preview_group = QGroupBox("已选列表预览")
        preview_layout = QVBoxLayout()
        self.stock_files_preview = QTextEdit()
        self.stock_files_preview.setReadOnly(True)
        self.stock_files_preview.setMaximumHeight(100)
        preview_layout.addWidget(self.stock_files_preview)
        preview_group.setLayout(preview_layout)
        stock_layout.addWidget(preview_group)
        
        # 连接复选框信号
        for checkbox in self.stock_checkboxes.values():
            checkbox.stateChanged.connect(self.update_stock_files_preview)
        
        stock_group.setLayout(stock_layout)
        layout.addWidget(stock_group)

    def clear_stock_files(self):
        """清空股票列表预览"""
        try:
            # 取消所有复选框的选中状态
            for checkbox in self.stock_checkboxes.values():
                checkbox.setChecked(False)
            
            # 清空预览文本
            self.stock_files_preview.setText("未选择任何股票列表文件")
            
            logging.info("已清空股票列表选择")
            
        except Exception as e:
            logging.error(f"清空股票列表时出错: {str(e)}", exc_info=True)
            QMessageBox.warning(self.parent, "错误", f"清空列表时出错: {str(e)}")

    def add_custom_stock_file(self):
        """添加自定义股票列表文件"""
        try:
            # 使用用户数据目录
            data_dir = get_data_directory()
            
            # 打开文件选择对话框，设置默认路径为data目录
            files, _ = QFileDialog.getOpenFileNames(
                self.parent,
                "选择股票代码列表文件",
                data_dir,  # 设置默认路径
                "CSV Files (*.csv);;All Files (*)"
            )
            
            if files:
                # 获取当前预览中的文件列表
                current_files = []
                current_text = self.stock_files_preview.toPlainText().strip()
                if current_text and current_text != "未选择任何股票列表文件":
                    current_files = current_text.split('\n')
                
                # 添加新选择的文件，避免重复
                for file in files:
                    if file not in current_files:
                        current_files.append(file)
                
                # 更新预览文本
                preview_text = "\n".join(current_files)
                self.stock_files_preview.setText(preview_text)
                
                logging.info(f"已添加自定义股票列表文件: {files}")
                
        except Exception as e:
            logging.error(f"添加自定义股票列表文件时出错: {str(e)}", exc_info=True)
            QMessageBox.warning(self.parent, "错误", f"添加文件时出错: {str(e)}")

    def update_stock_files_preview(self):
        """更新选中的股票文件预览"""
        try:
            # 使用code目录下的data文件夹
            data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
            selected_files = []
            
            # 获取当前预览中的自定义文件列表
            custom_files = []
            current_preview = self.stock_files_preview.toPlainText().strip()
            if current_preview and current_preview != "未选择任何股票列表文件":
                preview_lines = current_preview.split('\n')
                for line in preview_lines:
                    # 修改这里：排除所有预定义的文件名，包括自选清单
                    if line.strip() and not any(board_name in line for board_name in [
                        '上证A股', '深证A股', '创业板', '科创板', '沪深A股', '指数',
                        '中证500成分股', '沪深300成分股', '上证50成分股', 'otheridx.csv'  # 添加 otheridx.csv
                    ]):
                        custom_files.append(line.strip())
            
            # 添加预定义列表文件
            for stock_type, checkbox in self.stock_checkboxes.items():
                if checkbox.isChecked():
                    filename = None
                    if stock_type == 'indices':
                        filename = os.path.join(data_dir, "指数_股票列表.csv")
                    elif stock_type == 'zz500':
                        filename = os.path.join(data_dir, "中证500成分股_股票列表.csv")
                    elif stock_type == 'hs300':
                        filename = os.path.join(data_dir, "沪深300成分股_股票列表.csv")
                    elif stock_type == 'sz50':
                        filename = os.path.join(data_dir, "上证50成分股_股票列表.csv")
                    elif stock_type == 'custom':
                        custom_file = os.path.join(data_dir, "otheridx.csv")
                        if os.path.exists(custom_file):
                            filename = custom_file
                        else:
                            # 如果自选清单文件不存在，创建一个空文件
                            try:
                                with open(custom_file, 'w', encoding='utf-8') as f:
                                    f.write("code,name\n")  # 写入表头
                                filename = custom_file
                                logging.info(f"已创建新的自选清单文件: {custom_file}")
                            except Exception as e:
                                logging.error(f"创建自选清单文件失败: {str(e)}")
                    else:
                        board_names = {
                            'sh_a': '上证A股',
                            'sz_a': '深证A股',
                            'gem': '创业板',
                            'sci': '科创板',
                            'hs_a': '沪深A股'
                        }
                        filename = os.path.join(data_dir, f"{board_names[stock_type]}_股票列表.csv")
                    
                    if filename and os.path.exists(filename):
                        selected_files.append(filename)
                    else:
                        logging.warning(f"股票列表文件不存在: {filename}")
            
            # 合并预定义列表和自定义列表
            selected_files.extend(custom_files)
            
            # 只有当没有任何选中的文件时才显示"未选择任何股票列表文件"
            if not selected_files:
                self.stock_files_preview.setText("未选择任何股票列表文件")
            else:
                self.stock_files_preview.setText("\n".join(selected_files))
            
        except Exception as e:
            logging.error(f"更新股票文件预览时出错: {str(e)}", exc_info=True)
            self.stock_files_preview.setText("更新预览时出错")

    def add_period_group(self, layout, title_font):
        """添加周期选择组"""
        # 创建水平布局来容纳两个组
        h_layout = QHBoxLayout()

        # 周期类型组
        period_group = QGroupBox("周期类型")
        period_group.setFont(title_font)
        period_layout = QHBoxLayout()

        self.period_type_combo = QComboBox()
        self.period_type_combo.addItems(['tick', '1m', '5m', '1d'])
        self.period_type_combo.currentTextChanged.connect(self.update_field_checkboxes)
        period_layout.addWidget(self.period_type_combo)
        period_group.setLayout(period_layout)
        h_layout.addWidget(period_group)

        # 复权方式组
        dividend_group = QGroupBox("复权方式（仅针对下载数据）")
        dividend_group.setFont(title_font)
        dividend_layout = QVBoxLayout()

        # 添加说明文字
        note_label = QLabel("注：补充数据模式不涉及复权")
        note_label.setStyleSheet("color: gray; font-size: 12px;")
        dividend_layout.addWidget(note_label)

        # 复权选择下拉框
        self.dividend_type_combo = QComboBox()
        self.dividend_type_combo.addItem("不复权", "none")
        self.dividend_type_combo.addItem("前复权", "front")
        self.dividend_type_combo.addItem("后复权", "back")
        self.dividend_type_combo.addItem("等比前复权", "front_ratio")
        self.dividend_type_combo.addItem("等比后复权", "back_ratio")
        # 设置默认选项为前复权
        self.dividend_type_combo.setCurrentIndex(1)  # 设置为"前复权"
        dividend_layout.addWidget(self.dividend_type_combo)

        dividend_group.setLayout(dividend_layout)
        h_layout.addWidget(dividend_group)

        layout.addLayout(h_layout)

    def add_field_group(self, layout, title_font):
        """添加字段选择组"""
        self.field_group = QGroupBox("要存储的字段列表")
        self.field_group.setFont(title_font)
        self.field_layout = QGridLayout()
        self.field_checkboxes = {}
        self.field_group.setLayout(self.field_layout)
        layout.addWidget(self.field_group)

        # 初始化字段
        self.tick_fields = TICK_FIELDS
        self.kline_fields = KLINE_FIELDS

        self.update_field_checkboxes()

    def update_field_checkboxes(self):
        """更新字段复选框"""
        # 保存当前选中状态
        current_selections = {}
        if hasattr(self, 'field_checkboxes'):
            for field, checkbox in self.field_checkboxes.items():
                current_selections[field] = checkbox.isChecked()

        # 清除现有的复选框
        for i in reversed(range(self.field_layout.count())):
            self.field_layout.itemAt(i).widget().setParent(None)

        self.field_checkboxes.clear()

        period_type = self.period_type_combo.currentText()
        fields = self.tick_fields if period_type == 'tick' else self.kline_fields

        for i, (field, description) in enumerate(fields.items()):
            checkbox = QCheckBox(f"{description}")
            # 如果该字段之前被选中，保持选中状态
            if field in current_selections:
                checkbox.setChecked(current_selections[field])
            else:
                checkbox.setChecked(True)  # 新字段默认选中
            self.field_checkboxes[field] = checkbox
            self.field_layout.addWidget(checkbox, i // 4, i % 4)

    def add_date_group(self, layout, title_font):
        """添加日期选择组"""
        date_group = QGroupBox("日期范围")
        date_group.setFont(title_font)
        date_layout = QHBoxLayout()

        start_date_layout = QHBoxLayout()
        start_date_layout.addWidget(QLabel("起始日期:"))
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate(2024, 1, 1))
        start_date_layout.addWidget(self.start_date_edit)

        end_date_layout = QHBoxLayout()
        end_date_layout.addWidget(QLabel("结束日期:"))
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate(2024, 2, 1))
        end_date_layout.addWidget(self.end_date_edit)

        date_layout.addLayout(start_date_layout)
        date_layout.addLayout(end_date_layout)

        date_group.setLayout(date_layout)
        layout.addWidget(date_group)

    def add_time_group(self, layout, title_font):
        """添加时间段选择组"""
        time_group = QGroupBox("时间段")
        time_group.setFont(title_font)
        time_layout = QHBoxLayout()

        # 添加时间范围选择下拉框
        self.use_all_time_checkbox = QComboBox()
        self.use_all_time_checkbox.addItems(['指定时间段', '全天'])
        self.use_all_time_checkbox.setCurrentIndex(1)  # 默认选择"全天"
        self.use_all_time_checkbox.currentIndexChanged.connect(self.toggle_time_range)
        time_layout.addWidget(self.use_all_time_checkbox)

        # 添加时间选择控件
        time_layout.addWidget(QLabel("开始时间:"))
        self.start_time_edit = QTimeEdit()
        self.start_time_edit.setTime(QTime(9, 30))
        time_layout.addWidget(self.start_time_edit)

        time_layout.addWidget(QLabel("结束时间:"))
        self.end_time_edit = QTimeEdit()
        self.end_time_edit.setTime(QTime(15, 0))
        time_layout.addWidget(self.end_time_edit)

        # 根据当前选择设置时间编辑器的启用状态
        self.start_time_edit.setEnabled(False)
        self.end_time_edit.setEnabled(False)

        time_group.setLayout(time_layout)
        layout.addWidget(time_group)

    def toggle_time_range(self, index):
        """切换时间范围选择"""
        use_all_time = (index == 1)  # 1 表示选择了"全天"
        self.start_time_edit.setEnabled(not use_all_time)
        self.end_time_edit.setEnabled(not use_all_time)

    def add_download_section(self, layout):
        """添加下载按钮区域"""
        download_layout = QVBoxLayout()

        # 创建按钮布局
        button_layout = QHBoxLayout()

        # 下载数据按钮
        self.download_button = QPushButton("下载数据（数据下载时UI界面会进入未响应状态，如要终止下载直接关闭软件）")
        self.download_button.setMinimumHeight(40)
        self.download_button.clicked.connect(self.parent.download_data)
        button_layout.addWidget(self.download_button)

        # 补充数据按钮
        self.supplement_button = QPushButton("补充数据")
        self.supplement_button.setMinimumHeight(40)
        self.supplement_button.clicked.connect(self.parent.supplement_data)
        button_layout.addWidget(self.supplement_button)

        download_layout.addLayout(button_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setMinimumHeight(10)
        download_layout.addWidget(self.progress_bar)

        layout.addLayout(download_layout)
