"""
测试重构后的GUI代码
"""
import sys
import os

def test_imports():
    """测试所有模块的导入"""
    print("测试模块导入...")
    
    try:
        # 测试utils模块
        from utils.config import ICON_PATH, MIN_WINDOW_SIZE, get_screen_config
        from utils.styles import MAIN_WINDOW_STYLE
        from utils.helpers import setup_logging, load_icon
        print("✓ utils模块导入成功")
        
        # 测试components模块
        from components.download_thread import DownloadThread
        from components.data_cleaner import StockDataCleaner
        from components.cleaner_thread import CleanerThread
        from components.settings_dialog import SettingsDialog
        from components.splash_screen import CustomSplashScreen
        print("✓ components模块导入成功")
        
        # 测试ui模块
        from ui.download_interface import DownloadInterface
        from ui.cleaner_interface import CleanerInterface
        from ui.toolbar import Toolbar
        print("✓ ui模块导入成功")
        
        # 测试主GUI模块
        from gui_new import StockDataProcessorGUI
        print("✓ 主GUI模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        return False

def test_config():
    """测试配置模块"""
    print("\n测试配置模块...")
    
    try:
        from utils.config import ICON_PATH, TICK_FIELDS, KLINE_FIELDS, STOCK_TYPES
        
        # 检查路径是否存在
        print(f"图标路径: {ICON_PATH}")
        
        # 检查字段定义
        print(f"Tick字段数量: {len(TICK_FIELDS)}")
        print(f"K线字段数量: {len(KLINE_FIELDS)}")
        print(f"股票类型数量: {len(STOCK_TYPES)}")
        
        print("✓ 配置模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 配置模块测试失败: {e}")
        return False

def test_styles():
    """测试样式模块"""
    print("\n测试样式模块...")
    
    try:
        from utils.styles import MAIN_WINDOW_STYLE, BUTTON_STYLES, MENU_STYLE
        
        # 检查样式是否为字符串
        assert isinstance(MAIN_WINDOW_STYLE, str), "主窗口样式应该是字符串"
        assert isinstance(BUTTON_STYLES, dict), "按钮样式应该是字典"
        assert isinstance(MENU_STYLE, str), "菜单样式应该是字符串"
        
        print("✓ 样式模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 样式模块测试失败: {e}")
        return False

def test_helpers():
    """测试辅助函数模块"""
    print("\n测试辅助函数模块...")
    
    try:
        from utils.helpers import validate_date_range, validate_time_range, format_file_size
        
        # 测试验证函数
        from PyQt5.QtCore import QDate, QTime
        
        # 测试日期验证
        start_date = QDate(2024, 1, 1)
        end_date = QDate(2024, 12, 31)
        valid, msg = validate_date_range(start_date, end_date)
        assert valid, "日期范围验证应该通过"
        
        # 测试时间验证
        start_time = QTime(9, 30)
        end_time = QTime(15, 0)
        valid, msg = validate_time_range(start_time, end_time)
        assert valid, "时间范围验证应该通过"
        
        # 测试文件大小格式化
        size_str = format_file_size(1024)
        assert "KB" in size_str, "文件大小格式化应该包含单位"
        
        print("✓ 辅助函数模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 辅助函数模块测试失败: {e}")
        return False

def test_components():
    """测试组件模块"""
    print("\n测试组件模块...")
    
    try:
        from components.data_cleaner import StockDataCleaner
        
        # 测试数据清洗器
        cleaner = StockDataCleaner()
        assert cleaner.df is None, "初始化时数据应该为空"
        
        print("✓ 组件模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 组件模块测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试重构后的GUI代码...\n")
    
    # 添加当前目录到Python路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    tests = [
        test_imports,
        test_config,
        test_styles,
        test_helpers,
        test_components
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！重构成功！")
        return True
    else:
        print("❌ 部分测试失败，需要检查代码")
        return False

if __name__ == "__main__":
    # 需要PyQt5环境才能运行完整测试
    try:
        from PyQt5.QtWidgets import QApplication
        app = QApplication([])
        success = main()
        sys.exit(0 if success else 1)
    except ImportError:
        print("警告: 未安装PyQt5，跳过需要Qt的测试")
        # 只运行基本的导入测试
        if test_imports():
            print("✓ 基本导入测试通过")
        else:
            print("✗ 基本导入测试失败")
