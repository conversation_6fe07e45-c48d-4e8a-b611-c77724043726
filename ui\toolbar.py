"""
工具栏组件
"""
import webbrowser
from PyQt5.QtWidgets import QWidget, QHBoxLayout, QPushButton, QMenu
from PyQt5.QtCore import Qt, QPoint, pyqtSignal
from PyQt5 import QtCore
from utils.config import get_screen_config
from utils.helpers import load_icon

class Toolbar(QWidget):
    # 定义信号
    visualization_requested = pyqtSignal()
    settings_requested = pyqtSignal()
    update_check_requested = pyqtSignal()
    version_info_requested = pyqtSignal()
    
    def __init__(self, icon_path, parent=None):
        super().__init__(parent)
        self.icon_path = icon_path
        self.parent = parent
        self.setup_ui()
        
    def setup_ui(self):
        """设置工具栏UI"""
        self.setObjectName("toolbarWidget")
        
        # 获取屏幕配置
        screen_config = get_screen_config()
        self.icon_size = screen_config['icon_size']
        toolbar_height = screen_config['toolbar_height']
        toolbar_margins = screen_config['toolbar_margins']
        
        self.setFixedHeight(toolbar_height)
        
        # 创建工具栏布局
        toolbar_layout = QHBoxLayout(self)
        toolbar_layout.setObjectName("toolbarLayout")
        toolbar_layout.setContentsMargins(*toolbar_margins)
        toolbar_layout.setAlignment(Qt.AlignVCenter)
        
        # 添加可视化按钮
        self.add_visualization_button(toolbar_layout)
        
        # 添加设置按钮
        self.add_settings_button(toolbar_layout)
        
        # 添加版本按钮
        self.add_version_button(toolbar_layout)
        
        # 添加主页按钮
        self.add_home_button(toolbar_layout)
        
        # 添加一个弹性空间使按钮靠左对齐
        toolbar_layout.addStretch()
        
    def add_visualization_button(self, layout):
        """添加数据可视化按钮"""
        visualize_btn = QPushButton()
        visualize_btn.setIcon(load_icon(self.icon_path, 'visualize.png'))
        visualize_btn.setIconSize(QtCore.QSize(self.icon_size, self.icon_size))
        visualize_btn.setFixedSize(self.icon_size + 6, self.icon_size + 6)
        visualize_btn.setToolTip('数据可视化')
        visualize_btn.clicked.connect(self.visualization_requested.emit)
        visualize_btn.setObjectName("toolbarButton")
        layout.addWidget(visualize_btn)
        
    def add_settings_button(self, layout):
        """添加设置按钮"""
        settings_btn = QPushButton()
        settings_btn.setIcon(load_icon(self.icon_path, 'settings.png'))
        settings_btn.setIconSize(QtCore.QSize(self.icon_size, self.icon_size))
        settings_btn.setFixedSize(self.icon_size + 6, self.icon_size + 6)
        settings_btn.setToolTip('设置')
        settings_btn.clicked.connect(self.settings_requested.emit)
        settings_btn.setObjectName("toolbarButton") 
        layout.addWidget(settings_btn)
        
    def add_version_button(self, layout):
        """添加版本按钮"""
        version_btn = QPushButton()
        version_btn.setIcon(load_icon(self.icon_path, 'version.png'))
        version_btn.setIconSize(QtCore.QSize(self.icon_size, self.icon_size))
        version_btn.setFixedSize(self.icon_size + 8, self.icon_size + 8)
        version_btn.setToolTip('版本信息')
        version_btn.clicked.connect(self.show_version_menu)
        version_btn.setObjectName("toolbarButton")
        layout.addWidget(version_btn)
        
    def add_home_button(self, layout):
        """添加主页按钮"""
        home_btn = QPushButton()
        home_btn.setIcon(load_icon(self.icon_path, 'home.png'))
        home_btn.setIconSize(QtCore.QSize(self.icon_size, self.icon_size))
        home_btn.setFixedSize(self.icon_size + 8, self.icon_size + 8)
        home_btn.setToolTip('访问主页')
        home_btn.clicked.connect(lambda: self.open_url("https://khsci.com/khQuant"))
        home_btn.setObjectName("toolbarButton")
        layout.addWidget(home_btn)
        
    def show_version_menu(self):
        """显示版本相关的菜单"""
        menu = QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background-color: #2D2D2D;
                border: 1px solid #3D3D3D;
                padding: 5px;
            }
            QMenu::item {
                padding: 5px 20px;
                color: #E0E0E0;
            }
            QMenu::item:selected {
                background-color: #3D3D3D;
            }
        """)
        
        # 添加菜单项
        check_update_action = menu.addAction("检查更新")
        about_action = menu.addAction("关于软件")
        
        # 获取按钮位置
        button = self.sender()
        if button:
            # 显示菜单
            action = menu.exec_(button.mapToGlobal(QPoint(0, button.height())))
            
            # 处理菜单选择
            if action == check_update_action:
                self.update_check_requested.emit()
            elif action == about_action:
                self.version_info_requested.emit()
                
    def open_url(self, url):
        """打开指定URL"""
        webbrowser.open(url)
