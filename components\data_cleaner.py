"""
数据清洗器组件
"""
import pandas as pd
import logging

class StockDataCleaner:
    def __init__(self):
        self.df = None
        self.columns = []
        self.row_changes = {}
        self.deleted_rows = {}
        self.reset()
        
    def reset(self):
        self.df = None
        self.columns = []
        self.row_changes = {}
        self.deleted_rows = {}
        
    def load_data(self, file_path):
        self.reset()
        self.df = pd.read_csv(file_path)
        self.columns = self.df.columns.tolist()
        return self

    def clean_data(self):
        if self.df is None:
            raise ValueError("未加载数据。请先调用 load_data() 方法。")
        
        self.remove_duplicates()
        self.handle_missing_values()
        self.correct_data_types()
        self.remove_outliers()
        self.handle_non_trading_hours()
        self.sort_data()
        return self

    def remove_duplicates(self):
        initial_rows = len(self.df)
        
        # 识别时间戳列
        time_columns = [col for col in self.df.columns if 'time' in col.lower() or 'date' in col.lower()]
        
        if time_columns:
            # 首先按时间戳检查重复
            time_duplicates = self.df[self.df.duplicated(subset=time_columns, keep='first')]
            
            # 对于时间戳重复的行，进一步检查其他数据是否也重复
            full_duplicates = self.df[self.df.duplicated(keep='first')]
            
            # 保存两种重复情况的统计
            self.duplicate_stats = {
                'time_duplicate_count': len(time_duplicates),
                'full_duplicate_count': len(full_duplicates),
                'time_only_duplicates': len(time_duplicates) - len(full_duplicates)
            }
            
            # 可以选择保留时间戳相同但数据不同的记录
            # 这种情况可能是同一时刻的多笔交易
            self.df = self.df.drop_duplicates(keep='first')
            
            # 记录细节信息
            self.deleted_rows['remove_duplicates'] = {
                'time_duplicates': time_duplicates,
                'full_duplicates': full_duplicates
            }
            
            # 添加警告日志
            if len(time_duplicates) > len(full_duplicates):
                logging.warning(
                    f"发现{len(time_duplicates) - len(full_duplicates)}行时间戳重复但数据不完全相同的记录，"
                    "这可能表示同一时刻的多笔交易"
                )
                
        else:
            # 如果没有识别到时间列，按所有列查重
            duplicates = self.df[self.df.duplicated()]
            self.df = self.df.drop_duplicates(inplace=True)
            self.deleted_rows['remove_duplicates'] = duplicates
        
        final_rows = len(self.df)
        self.row_changes['remove_duplicates'] = initial_rows - final_rows

    def handle_missing_values(self):
        initial_rows = len(self.df)
        rows_with_missing = self.df[self.df.isnull().any(axis=1)]
        
        price_columns = [col for col in ['open', 'high', 'low', 'close'] if col in self.columns]
        if price_columns:
            self.df[price_columns] = self.df[price_columns].ffill()
        
        volume_columns = [col for col in ['volume'] if col in self.columns]
        if volume_columns:
            self.df[volume_columns] = self.df[volume_columns].fillna(0)
        
        self.df.dropna(inplace=True)
        final_rows = len(self.df)
        self.row_changes['handle_missing_values'] = initial_rows - final_rows
        self.deleted_rows['handle_missing_values'] = rows_with_missing

    def correct_data_types(self):
        date_columns = [col for col in self.columns if 'date' in col.lower()]
        for col in date_columns:
            self.df[col] = pd.to_datetime(self.df[col], errors='coerce')
        
        numeric_columns = [col for col in ['open', 'high', 'low', 'close', 'volume'] if col in self.columns]
        for col in numeric_columns:
            self.df[col] = pd.to_numeric(self.df[col], errors='coerce')

    def remove_outliers(self):
        initial_rows = len(self.df)
        outliers = pd.DataFrame()
        price_columns = [col for col in ['open', 'high', 'low', 'close'] if col in self.columns]
        for col in price_columns:
            Q1 = self.df[col].quantile(0.25)
            Q3 = self.df[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 5 * IQR
            upper_bound = Q3 + 5 * IQR
            col_outliers = self.df[(self.df[col] < lower_bound) | (self.df[col] > upper_bound)]
            outliers = pd.concat([outliers, col_outliers])
            self.df = self.df[(self.df[col] >= lower_bound) & (self.df[col] <= upper_bound)]
        final_rows = len(self.df)
        self.row_changes['remove_outliers'] = initial_rows - final_rows
        self.deleted_rows['remove_outliers'] = outliers

    def handle_non_trading_hours(self):
        initial_rows = len(self.df)
        non_trading_hours = pd.DataFrame()  # 初始化变量
        if 'time' in self.columns:
            morning_start = pd.to_datetime('09:30:00').time()
            morning_end = pd.to_datetime('11:30:00').time()
            afternoon_start = pd.to_datetime('13:00:00').time()
            afternoon_end = pd.to_datetime('15:00:00').time()

            self.df['time'] = pd.to_datetime(self.df['time'], errors='coerce').dt.time

            # 修复括号匹配问题
            non_trading_hours = self.df[
                ~(((self.df['time'] >= morning_start) & (self.df['time'] <= morning_end)) |
                  ((self.df['time'] >= afternoon_start) & (self.df['time'] <= afternoon_end)))
            ]

            self.df = self.df[
                ((self.df['time'] >= morning_start) & (self.df['time'] <= morning_end)) |
                ((self.df['time'] >= afternoon_start) & (self.df['time'] <= afternoon_end))
            ]
        
        final_rows = len(self.df)
        self.row_changes['handle_non_trading_hours'] = initial_rows - final_rows
        self.deleted_rows['handle_non_trading_hours'] = non_trading_hours

    def sort_data(self):
        sort_columns = [col for col in self.columns if 'date' in col.lower() or 'time' in col.lower()]
        if sort_columns:
            self.df.sort_values(by=sort_columns, inplace=True)

    def get_cleaned_data(self):
        return self.df

    def save_cleaned_data(self, file_path):
        self.df.to_csv(file_path, index=False)

    def get_column_info(self):
        return {
            'all_columns': self.columns,
            'date_columns': [col for col in self.columns if 'date' in col.lower()],
            'time_columns': [col for col in self.columns if 'time' in col.lower()],
            'price_columns': [col for col in ['open', 'high', 'low', 'close'] if col in self.columns],
            'volume_columns': [col for col in ['volume'] if col in self.columns]
        }

    def get_data_info(self):
        return {
            'shape': self.df.shape,
            'dtypes': self.df.dtypes.to_dict(),
            'missing_values': self.df.isnull().sum().to_dict(),
            'duplicates': self.df.duplicated().sum(),
            'numeric_stats': self.df.describe().to_dict(),
            'row_changes': self.row_changes,
            'deleted_rows': self.deleted_rows
        }
