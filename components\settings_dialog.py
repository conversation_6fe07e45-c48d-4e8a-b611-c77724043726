"""
设置对话框组件
"""
import os
import logging
import webbrowser
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                             QPushButton, QGroupBox, QMessageBox, QFileDialog, 
                             QProgressDialog)
from PyQt5.QtCore import Qt, QSettings
from PyQt5.QtGui import QFont
from khQTTools import get_and_save_stock_list
from version import get_version_info
from utils.styles import SETTINGS_DIALOG_STYLE, BUTTON_STYLES

class SettingsDialog(QDialog):
    """设置对话框类"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.settings = QSettings('KHQuant', 'StockAnalyzer')
        self.confirmed_exit = False
        
        # 设置窗口标志
        self.setWindowFlags(Qt.Dialog | Qt.WindowStaysOnTopHint)
        self.setWindowModality(Qt.ApplicationModal)
        
        self.initUI()

    def initUI(self):
        """设置对话框UI初始化"""
        self.setWindowTitle('软件设置')
        self.setMinimumWidth(500)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setSpacing(15)  # 增加组件之间的间距
        
        # 首先添加股票列表管理组
        stock_list_group = QGroupBox("股票列表管理")
        stock_list_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #3D3D3D;
                border-radius: 5px;
                margin-top: 12px;
                padding-top: 15px;
                color: #E0E0E0;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 7px;
                padding: 0 3px;
            }
        """)
        stock_list_layout = QVBoxLayout()
        
        # 添加更新按钮
        update_stock_list_btn = QPushButton("更新成分股列表（运行时需耐心等待，无需频繁更新")
        update_stock_list_btn.clicked.connect(self.update_stock_list)
        stock_list_layout.addWidget(update_stock_list_btn)
        
        stock_list_group.setLayout(stock_list_layout)
        layout.addWidget(stock_list_group)

        # 客户端路径设置组
        client_group = QGroupBox("客户端设置")
        client_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #3D3D3D;
                border-radius: 5px;
                margin-top: 12px;
                padding-top: 15px;
                color: #E0E0E0;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 7px;
                padding: 0 3px;
            }
        """)
        path_layout = QVBoxLayout()
        path_label = QLabel("miniQMT客户端路径:")
        path_label.setStyleSheet("color: #E0E0E0;")
        path_layout.addWidget(path_label)
        
        # 路径输入和浏览按钮放在单独的水平布局中
        input_layout = QHBoxLayout()
        self.client_path_edit = QLineEdit()
        self.client_path_edit.setMinimumWidth(400)  # 增加宽度
        default_path = r"C:\国金证券QMT交易端\bin.x64\XtItClient.exe"
        saved_path = self.settings.value('client_path', default_path)
        self.client_path_edit.setText(saved_path)
        self.client_path_edit.setToolTip("请选择miniQMT客户端启动程序XtItClient.exe")
        self.client_path_edit.setStyleSheet("""
            QLineEdit {
                background-color: #2D2D2D;
                border: 1px solid #3D3D3D;
                border-radius: 3px;
                padding: 5px;
                color: #E0E0E0;
            }
        """)
        
        browse_button = QPushButton("浏览")
        browse_button.setFixedWidth(60)
        browse_button.clicked.connect(self.browse_client)
        browse_button.setStyleSheet("""
            QPushButton {
                background-color: #3D3D3D;
                color: #E0E0E0;
                border: none;
                padding: 5px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #4D4D4D;
            }
        """)
        
        input_layout.addWidget(self.client_path_edit)
        input_layout.addWidget(browse_button)
        path_layout.addLayout(input_layout)
        client_group.setLayout(path_layout)
        layout.addWidget(client_group)
        
        # 版本信息组
        version_group = QGroupBox("版本信息")
        version_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #3D3D3D;
                border-radius: 5px;
                margin-top: 12px;
                padding-top: 15px;
                color: #E0E0E0;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 7px;
                padding: 0 3px;
            }
        """)
        version_layout = QVBoxLayout()
        version_info = get_version_info()
        version_label = QLabel(f"当前版本：v{version_info['version']}")
        version_label.setStyleSheet("color: #E0E0E0;")
        version_layout.addWidget(version_label)
        version_group.setLayout(version_layout)
        layout.addWidget(version_group)
        
        # 底部按钮布局
        button_layout = QHBoxLayout()
        
        # 添加反馈问题按钮（靠左）
        feedback_button = QPushButton("反馈问题")
        feedback_button.setFixedWidth(100)
        feedback_button.setStyleSheet(BUTTON_STYLES['feedback'])
        feedback_button.clicked.connect(self.open_feedback_page)
        button_layout.addWidget(feedback_button)
        
        # 添加弹性空间，使保存和关闭按钮靠右
        button_layout.addStretch()
        
        # 保存和关闭按钮（靠右）
        save_button = QPushButton("保存设置")
        save_button.setFixedWidth(100)
        save_button.setStyleSheet(BUTTON_STYLES['save'])
        save_button.clicked.connect(self.save_settings)
        
        close_button = QPushButton("关闭")
        close_button.setFixedWidth(100)
        close_button.setStyleSheet(BUTTON_STYLES['close'])
        close_button.clicked.connect(self.close)
        
        button_layout.addWidget(save_button)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        
        # 设置整体背景色
        self.setStyleSheet(SETTINGS_DIALOG_STYLE)

    def browse_client(self):
        """浏览选择客户端路径"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择miniQMT客户端程序",
            self.client_path_edit.text(),
            "可执行文件 (*.exe)"
        )
        if file_path:
            self.client_path_edit.setText(file_path)
            
    def save_settings(self):
        """保存设置"""
        try:
            client_path = self.client_path_edit.text().strip()
            if not os.path.exists(client_path):
                QMessageBox.warning(self, "警告", "指定的客户端路径不存在")
                return
                
            self.settings.setValue('client_path', client_path)
            QMessageBox.information(self, "成功", "设置已保存")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存设置时出错: {str(e)}")

    def open_feedback_page(self):
        """打开反馈问题页面"""
        url = "https://khsci.com/khQuant/feedback"
        webbrowser.open(url)

    def update_stock_list(self):
        """更新股票列表"""
        try:
            # 禁用按钮
            update_stock_list_btn = self.findChild(QPushButton, "update_stock_list_btn")
            if update_stock_list_btn:
                update_stock_list_btn.setEnabled(False)
            # 创建进度对话框
            self.progress_dialog = QProgressDialog("正在更新股票列表...", None, 0, 0, self)
            self.progress_dialog.setWindowModality(Qt.WindowModal)
            self.progress_dialog.setCancelButton(None)
            self.progress_dialog.show()
            
            # 修改这里：使用code目录下的data文件夹
            data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
            os.makedirs(data_dir, exist_ok=True)
            
            # 获取更新线程
            update_thread = get_and_save_stock_list(data_dir)
            
            # 连接信号
            update_thread.progress.connect(self.show_update_progress)
            update_thread.finished.connect(self.handle_update_finished)
            
            # 保存线程引用
            self.update_thread = update_thread
            
        except Exception as e:
            # 恢复按钮
            if update_stock_list_btn:
                update_stock_list_btn.setEnabled(True)
            self.progress_dialog.close()
            logging.error(f"更新股票列表时出错: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"更新股票列表时出错: {str(e)}")

    def show_update_progress(self, message):
        """显示更新进度"""
        if hasattr(self, 'progress_dialog'):
            self.progress_dialog.setLabelText(message)

    def handle_update_finished(self, success, message):
        """处理更新完成"""
        if hasattr(self, 'progress_dialog'):
            self.progress_dialog.close()
        
        if success:
            QMessageBox.information(self, "成功", "股票列表更新成功！")
        else:
            QMessageBox.warning(self, "失败", f"更新股票列表失败：{message}")
        
        # 清理线程
        if hasattr(self, 'update_thread'):
            self.update_thread = None
