"""
清洗线程组件
"""
import os
import logging
from PyQt5.QtCore import QThread, pyqtSignal

class CleanerThread(QThread):
    progress_updated = pyqtSignal(int, int)
    cleaning_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, cleaner, folder_path, operations):
        super().__init__()
        self.cleaner = cleaner
        self.folder_path = folder_path
        self.operations = operations

    def run(self):
        try:
            csv_files = [f for f in os.listdir(self.folder_path) if f.endswith('.csv')]
            total_files = len(csv_files)
            cleaning_info = {}

            for file_index, file in enumerate(csv_files):
                file_path = os.path.join(self.folder_path, file)
                
                # 创建临时备份
                backup_path = file_path + '.bak'
                with open(file_path, 'r', encoding='utf-8') as source:
                    with open(backup_path, 'w', encoding='utf-8') as target:
                        target.write(source.read())
                
                try:
                    self.cleaner.load_data(file_path)
                    before_info = self.cleaner.get_data_info()
                    
                    total_steps = len(self.operations)
                    for i, operation in enumerate(self.operations):
                        if hasattr(self.cleaner, operation):
                            getattr(self.cleaner, operation)()
                        
                        file_progress = int((i + 1) / total_steps * 100)
                        total_progress = int((file_index * 100 + file_progress) / total_files)
                        self.progress_updated.emit(file_progress, total_progress)
                    
                    self.cleaner.save_cleaned_data(file_path)
                    after_info = self.cleaner.get_data_info()
                    
                    cleaning_info[file] = {
                        'before': before_info,
                        'after': after_info
                    }
                    
                    os.remove(backup_path)
                    
                except Exception as e:
                    if os.path.exists(backup_path):
                        os.replace(backup_path, file_path)
                    raise e

            self.cleaning_completed.emit(cleaning_info)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
