"""
下载线程组件
"""
import os
import logging
from PyQt5.QtCore import QThread, pyqtSignal
from khQTTools import download_and_store_data

class DownloadThread(QThread):
    progress = pyqtSignal(int)
    finished = pyqtSignal(bool, str)
    error = pyqtSignal(str)

    def __init__(self, params, parent=None):
        super().__init__(parent)
        self.params = params
        self.running = True
        logging.info(f"初始化下载线程，参数: {params}")

    def run(self):
        try:
            if not self.running:
                return
                
            logging.info("下载线程开始运行")
            
            # 参数验证
            if not self.params.get('local_data_path'):
                raise ValueError("数据存储路径未设置")
                
            if not self.params.get('stock_files'):
                raise ValueError("股票代码列表为空")
                
            if not self.params.get('field_list'):
                raise ValueError("字段列表为空")

            def progress_callback(percent):
                if not self.running:
                    raise InterruptedError("下载被中断")
                try:
                    self.progress.emit(percent)
                except Exception as e:
                    logging.error(f"进度错误: {str(e)}")

            # 分离指数文件和普通股票文件
            index_files = []
            stock_files = []
            for file_path in self.params['stock_files']:
                if '指数_股票列表' in file_path:
                    index_files.append(file_path)
                else:
                    stock_files.append(file_path)

            # 分别处理指数和普通股票
            total_files = len(index_files) + len(stock_files)
            current_progress = 0

            # 下载指数数据
            if index_files:
                try:
                    params_index = {
                        'local_data_path': self.params['local_data_path'],
                        'stock_files': index_files,
                        'field_list': self.params['field_list'],
                        'period_type': self.params['period_type'],
                        'start_date': self.params['start_date'],
                        'end_date': self.params['end_date'],
                        'time_range': self.params.get('time_range', 'all')
                    }
                    # 计算进度的回调函数
                    progress_cb = lambda p: self.progress.emit(
                        int(current_progress * 100 / total_files + p * len(index_files) / total_files)
                    )
                    download_and_store_data(**params_index, progress_callback=progress_cb)
                    current_progress += len(index_files)
                except Exception as e:
                    logging.error(f"下载指数数据时出错: {str(e)}")
                    raise

            # 下载普通股票数据
            if stock_files:
                try:
                    params_stock = {
                        'local_data_path': self.params['local_data_path'],
                        'stock_files': stock_files,
                        'field_list': self.params['field_list'],
                        'period_type': self.params['period_type'],
                        'start_date': self.params['start_date'],
                        'end_date': self.params['end_date'],
                        'time_range': self.params.get('time_range', 'all')
                    }
                    # 计算进度的回调函数
                    progress_cb = lambda p: self.progress.emit(
                        int(current_progress * 100 / total_files + p * len(stock_files) / total_files)
                    )
                    download_and_store_data(**params_stock, progress_callback=progress_cb)
                except Exception as e:
                    logging.error(f"下载股票数据时出错: {str(e)}")
                    raise

            if self.running:
                self.finished.emit(True, "数据下载完成！")
                
        except Exception as e:
            error_msg = f"下载过程中发生错误: {str(e)}"
            logging.error(error_msg, exc_info=True)
            import traceback
            logging.error(traceback.format_exc())
            
            if self.running:
                self.error.emit(error_msg)
                self.finished.emit(False, error_msg)

    def stop(self):
        self.running = False
