"""
辅助函数模块
"""
import os
import sys
import logging
import psutil
from datetime import datetime
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QIcon, QPixmap, QPainter, QPen, QColor
from PyQt5.QtCore import Qt

def setup_logging():
    """配置日志系统"""
    try:
        # 在这里设置内部标志
        ENABLE_LOGGING = True  # 将标志设置为 True 开启完整日志，False 则只记录错误
        
        # 确定日志目录路径
        if getattr(sys, 'frozen', False):
            # 打包环境下，使用可执行文件所在目录
            base_path = os.path.dirname(sys.executable)
        else:
            # 开发环境下，使用当前文件所在目录
            base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        logs_dir = os.path.join(base_path, 'logs')
        os.makedirs(logs_dir, exist_ok=True)
        
        # 生成日志文件名（包含时间戳）
        log_filename = os.path.join(logs_dir, f"app_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        # 配置日志格式
        log_format = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        
        # 配置根日志记录器
        root_logger = logging.getLogger()
        
        # 清除已有的处理器（避免重复）
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        if ENABLE_LOGGING:
            # 启用完整日志时的配置
            root_logger.setLevel(logging.DEBUG)
            
            # 添加文件处理器
            file_handler = logging.FileHandler(log_filename, encoding='utf-8')
            file_handler.setFormatter(log_format)
            file_handler.setLevel(logging.DEBUG)
            root_logger.addHandler(file_handler)
            
            # 添加控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(log_format)
            console_handler.setLevel(logging.DEBUG)
            root_logger.addHandler(console_handler)
            
            # 记录启动信息
            logging.info("="*50)
            logging.info("应用程序启动")
            logging.info(f"日志文件路径: {log_filename}")
            logging.info(f"运行模式: {'打包环境' if getattr(sys, 'frozen', False) else '开发环境'}")
            logging.info(f"Python版本: {sys.version}")
            logging.info(f"操作系统: {sys.platform}")
            logging.info("="*50)
        else:
            # 仅记录错误日志时的配置
            root_logger.setLevel(logging.ERROR)
            
            # 只添加文件处理器，用于记录错误
            file_handler = logging.FileHandler(log_filename, encoding='utf-8')
            file_handler.setFormatter(log_format)
            file_handler.setLevel(logging.ERROR)
            root_logger.addHandler(file_handler)
        
        return log_filename
        
    except Exception as e:
        print(f"配置日志系统时出错: {str(e)}")
        return None

def is_software_running(process_name):
    """检查指定的进程是否正在运行"""
    for proc in psutil.process_iter(['name']):
        if proc.info['name'].lower() == process_name.lower():
            return True
    return False

def create_colored_pixmap(color, size=20):
    """创建指定颜色的圆形像素图"""
    pixmap = QPixmap(size, size)
    pixmap.fill(Qt.transparent)
    painter = QPainter(pixmap)
    painter.setBrush(QColor(color))
    painter.setPen(Qt.NoPen)
    painter.drawEllipse(0, 0, size, size)
    painter.end()
    return pixmap

def create_fallback_icon(icon_path, icon_name):
    """创建图标不存在时的备用图标"""
    try:
        # 确保图标目录存在
        if not os.path.exists(icon_path):
            os.makedirs(icon_path)
        
        full_icon_path = os.path.join(icon_path, icon_name)
        
        # 创建一个简单的默认图标
        pixmap = QPixmap(32, 32)
        pixmap.fill(QColor('#0078d7'))  # 使用蓝色背景
        
        # 添加一个简单的图案
        painter = QPainter(pixmap)
        painter.setPen(QPen(QColor('white'), 2))
        painter.drawRect(8, 8, 16, 16)
        painter.drawLine(8, 16, 24, 16)
        painter.drawLine(16, 8, 16, 24)
        painter.end()
        
        # 保存图标
        pixmap.save(full_icon_path)
        
        return QIcon(full_icon_path)
    except Exception as e:
        logging.error(f"创建备用图标时出错: {str(e)}", exc_info=True)
        return QIcon()  # 返回空图标作为最后的回退方案

def load_icon(icon_path, icon_name):
    """统一的图标加载方法"""
    full_icon_path = os.path.join(icon_path, icon_name)
    if os.path.exists(full_icon_path):
        return QIcon(full_icon_path)
    else:
        logging.warning(f"图标文件不存在: {full_icon_path}")
        fallback_icon = create_fallback_icon(icon_path, icon_name)
        # 如果创建备用图标失败，返回一个空的QIcon
        return fallback_icon if fallback_icon else QIcon()

def calculate_window_geometry():
    """计算窗口几何尺寸"""
    from .config import MIN_WINDOW_SIZE, WINDOW_SIZE_RATIO, MAX_WINDOW_SIZE_RATIO
    
    # 获取屏幕分辨率
    screen = QApplication.primaryScreen().geometry()
    screen_width = screen.width()
    screen_height = screen.height()
    
    # 计算窗口大小：
    # 1. 首先尝试使用屏幕大小的75%
    # 2. 如果计算出的大小小于最小尺寸，则使用最小尺寸
    # 3. 如果计算出的大小大于屏幕大小的90%，则使用90%
    window_width = int(screen_width * WINDOW_SIZE_RATIO)
    window_height = int(screen_height * WINDOW_SIZE_RATIO)
    
    # 确保窗口大小不小于最小尺寸
    window_width = max(window_width, MIN_WINDOW_SIZE)
    window_height = max(window_height, MIN_WINDOW_SIZE)
    
    # 确保窗口大小不超过屏幕90%
    max_width = int(screen_width * MAX_WINDOW_SIZE_RATIO)
    max_height = int(screen_height * MAX_WINDOW_SIZE_RATIO)
    window_width = min(window_width, max_width)
    window_height = min(window_height, max_height)
    
    # 计算居中位置
    x_position = (screen_width - window_width) // 2
    y_position = (screen_height - window_height) // 2
    
    return x_position, y_position, window_width, window_height

def open_url(url):
    """打开指定URL"""
    import webbrowser
    webbrowser.open(url)

def validate_date_range(start_date, end_date):
    """验证日期范围"""
    if end_date < start_date:
        return False, "结束日期不能早于开始日期"
    return True, ""

def validate_time_range(start_time, end_time):
    """验证时间范围"""
    if start_time >= end_time:
        return False, "开始时间必须早于结束时间"
    return True, ""

def format_file_size(size_bytes):
    """格式化文件大小"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    else:
        return f"{size_bytes / (1024 * 1024):.2f} MB"
