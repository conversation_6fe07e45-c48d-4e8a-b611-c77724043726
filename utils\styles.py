"""
样式定义模块
"""

# 主窗口样式
MAIN_WINDOW_STYLE = """
QWidget#toolbarWidget {
    background-color: #2D2D2D;
    border-top: 1px solid #3D3D3D;
    border-bottom: 1px solid #3D3D3D;
}
QPushButton#toolbarButton {
    background-color: transparent;
    border: none;
    border-radius: 5px;
    padding: 5px;
}
QPushButton#toolbarButton:hover {
    background-color: #3D3D3D;
}
QPushButton#toolbarButton:pressed {
    background-color: #1E1E1E;
}
QMainWindow {
    background-color: #1E1E1E;
    border: 1px solid #3D3D3D;
}
QWidget {
    background-color: #1E1E1E;
    color: #E0E0E0;
}
QPushButton#minButton, QPushButton#maxButton, QPushButton#closeButton {
    background-color: transparent;
    color: #E0E0E0;
    border: none;
    font-size: 24px;
}
QPushButton#minButton:hover, QPushButton#maxButton:hover {
    background-color: #3D3D3D;
}
QPushButton#closeButton:hover {
    background-color: #E81123;
}
QScrollArea {
    border: none;
}
QGroupBox {
    border: 2px solid #3D3D3D;
    border-radius: 5px;
    margin-top: 20px;
    padding-top: 10px;
    font-weight: bold;
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    top: 8px;
    padding: 0 3px 0 3px;
    font-size: 18px;
}
QLabel {
    color: #E0E0E0;
}
QLineEdit, QTextEdit, QDateEdit, QTimeEdit, QComboBox {
    background-color: #2D2D2D;
    border: 1px solid #3D3D3D;
    border-radius: 3px;
    padding: 5px;
    color: #E0E0E0;
}
QPushButton {
    background-color: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4A4A4A, stop:1 #2E2E2E);
    border: none;
    color: #E0E0E0;
    padding: 8px;
    border-radius: 3px;
}
QPushButton:hover {
    background-color: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #5A5A5A, stop:1 #3E3E3E);
}
QPushButton:pressed {
    background-color: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #3A3A3A, stop:1 #1E1E1E);
}
QProgressBar {
    border: 2px solid #3D3D3D;
    border-radius: 5px;
    text-align: center;
}
QProgressBar::chunk {
    background-color: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #3DAEE9, stop:1 #2980B9);
}
QCheckBox {
    spacing: 5px;
}
QCheckBox::indicator {
    width: 18px;
    height: 18px;
}
QCheckBox::indicator:unchecked {
    border: 2px solid #3D3D3D;
    background-color: #2D2D2D;
}
QCheckBox::indicator:checked {
    border: 2px solid #3DAEE9;
    background-color: #3DAEE9;
}
"""

# 设置对话框样式
SETTINGS_DIALOG_STYLE = """
QDialog {
    background-color: #1E1E1E;
}
QGroupBox {
    border: 1px solid #3D3D3D;
    border-radius: 5px;
    margin-top: 12px;
    padding-top: 15px;
    color: #E0E0E0;
}
QGroupBox::title {
    subcontrol-origin: margin;
    left: 7px;
    padding: 0 3px;
}
"""

# 按钮样式
BUTTON_STYLES = {
    'feedback': """
        QPushButton {
            background-color: #2D2D2D;
            color: #E0E0E0;
            border: none;
            padding: 5px 15px;
            border-radius: 2px;
        }
        QPushButton:hover {
            background-color: #3D3D3D;
        }
    """,
    'save': """
        QPushButton {
            background-color: #0078D7;
            color: white;
            border: none;
            padding: 5px 15px;
            border-radius: 2px;
        }
        QPushButton:hover {
            background-color: #1984D8;
        }
    """,
    'close': """
        QPushButton {
            background-color: #3D3D3D;
            color: #E0E0E0;
            border: none;
            padding: 5px 15px;
            border-radius: 2px;
        }
        QPushButton:hover {
            background-color: #4D4D4D;
        }
    """
}

# 输入框样式
INPUT_STYLE = """
QLineEdit {
    background-color: #2D2D2D;
    border: 1px solid #3D3D3D;
    border-radius: 3px;
    padding: 5px;
    color: #E0E0E0;
}
"""

# 菜单样式
MENU_STYLE = """
QMenu {
    background-color: #2D2D2D;
    border: 1px solid #3D3D3D;
    padding: 5px;
}
QMenu::item {
    padding: 5px 20px;
    color: #E0E0E0;
}
QMenu::item:selected {
    background-color: #3D3D3D;
}
"""

# 启动画面进度条样式
SPLASH_PROGRESS_STYLE = """
QProgressBar {
    border: 2px solid #2196F3;
    border-radius: 5px;
    background-color: #1E1E1E;
    text-align: center;
}
QProgressBar::chunk {
    background: qlineargradient(
        x1:0, y1:0, x2:1, y2:0,
        stop:0 #2196F3,
        stop:1 #64B5F6
    );
    border-radius: 3px;
}
"""

# 自定义标签样式
CUSTOM_LABEL_STYLE = """
QLabel {
    color: #3DAEE9;
    text-decoration: underline;
    cursor: pointer;
}
QLabel:hover {
    color: #2980B9;
}
"""

# 消息框样式
MESSAGE_BOX_STYLE = """
QMessageBox {
    background-color: #F0F0F0;
    border: 1px solid #D0D0D0;
    border-radius: 10px;
}
QMessageBox QLabel {
    background-color: #F0F0F0;
    color: #2C3E50;
    font-size: 24px;
    padding: 20px;
}
"""

MESSAGE_BOX_BUTTON_STYLE = """
QPushButton {
    font-size: 18px;
    background-color: #808080;
    color: white;
    border: none;
    padding: 8px;
    border-radius: 6px;
}
QPushButton:hover {
    background-color: #909090;
}
QPushButton:pressed {
    background-color: #707070;
}
"""
