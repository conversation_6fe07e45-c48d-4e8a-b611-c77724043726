"""
配置常量和路径定义
"""
import os
import sys

# 获取当前文件的上级目录
PARENT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(PARENT_DIR)

# 日志目录
LOGS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
os.makedirs(LOGS_DIR, exist_ok=True)

# 图标路径
if getattr(sys, 'frozen', False):
    # 打包后的环境，图标文件在 _internal/icons 目录下
    ICON_PATH = os.path.join(os.path.dirname(sys.executable), '_internal', 'icons')
else:
    # 开发环境
    ICON_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'icons')

# 确保图标目录存在
os.makedirs(ICON_PATH, exist_ok=True)

# 窗口配置
MIN_WINDOW_SIZE = 1000
WINDOW_SIZE_RATIO = 0.75
MAX_WINDOW_SIZE_RATIO = 0.9

# 工具栏配置
TOOLBAR_HEIGHT_2K = 70
TOOLBAR_HEIGHT_1080P = 50
TOOLBAR_HEIGHT_DEFAULT = 40

# 图标大小配置
ICON_SIZE_2K = 60
ICON_SIZE_1080P = 40
ICON_SIZE_DEFAULT = 32

# 边框厚度
BORDER_THICKNESS = 20

# 默认路径
DEFAULT_CLIENT_PATH = r"C:\国金证券QMT交易端\bin.x64\XtItClient.exe"
DEFAULT_DATA_PATH = "D:/stock_data"

# 字段定义
TICK_FIELDS = {
    "lastPrice": "最新价",
    "open": "开盘价",
    "high": "最高价",
    "low": "最低价",
    "lastClose": "前收盘价",
    "amount": "成交总额",
    "volume": "成交总量",
    "pvolume": "原始成交总量",
    "stockStatus": "证券状态",
    "openInt": "持仓量",
    "lastSettlementPrice": "前结算",
    "askPrice": "委卖价",
    "bidPrice": "委买价",
    "askVol": "委卖量",
    "bidVol": "委买量"
}

KLINE_FIELDS = {
    "open": "开盘价",
    "high": "最高价",
    "low": "最低价",
    "close": "收盘价",
    "volume": "成交量",
    "amount": "成交额",
    "settelementPrice": "今结算",
    "openInterest": "持仓量",
    "preClose": "前收价",
    "suspendFlag": "停牌标记"
}

# 股票类型映射
STOCK_TYPES = {
    'hs_a': '沪深A股',
    'gem': '创业板',
    'sci': '科创板',
    'sh_a': '上证A股',
    'zz500': '中证500成分股',
    'hs300': '沪深300成分股',
    'sz50': '上证50成分股',
    'indices': '常用指数',
    'custom': '自选清单'
}

# 清洗操作定义
CLEANING_OPERATIONS = [
    ('remove_duplicates', '删除重复行'),
    ('handle_missing_values', '处理缺失值'),
    ('correct_data_types', '修正数据类型'),
    ('remove_outliers', '移除异常值'),
    ('handle_non_trading_hours', '处理非交易时间'),
    ('sort_data', '排序数据')
]

def get_data_directory():
    """获取数据存储目录"""
    try:
        # 在用户文档目录下创建应用数据文件夹
        if sys.platform == 'win32':
            documents_path = os.path.join(os.path.expanduser('~'), 'Documents')
            data_dir = os.path.join(documents_path, 'KHQuant', 'data')
        else:
            # 其他操作系统的处理
            data_dir = os.path.join(os.path.expanduser('~'), '.khquant', 'data')
            
        # 确保目录存在
        os.makedirs(data_dir, exist_ok=True)
        return data_dir
        
    except Exception as e:
        # 返回一个默认路径
        return os.path.join(os.path.expanduser('~'), 'KHQuant_Data')

def get_screen_config():
    """根据屏幕分辨率获取配置"""
    from PyQt5.QtWidgets import QApplication
    
    screen = QApplication.primaryScreen().geometry()
    screen_width = screen.width()
    screen_height = screen.height()
    
    # 根据屏幕宽度设置不同的配置
    if screen_width >= 2560:  # 2K及以上分辨率
        return {
            'icon_size': ICON_SIZE_2K,
            'toolbar_height': TOOLBAR_HEIGHT_2K,
            'toolbar_margins': (20, 10, 20, 10)
        }
    elif screen_width >= 1920:  # 1080P
        return {
            'icon_size': ICON_SIZE_1080P,
            'toolbar_height': TOOLBAR_HEIGHT_1080P,
            'toolbar_margins': (15, 8, 15, 8)
        }
    else:  # 较低分辨率
        return {
            'icon_size': ICON_SIZE_DEFAULT,
            'toolbar_height': TOOLBAR_HEIGHT_DEFAULT,
            'toolbar_margins': (10, 5, 10, 5)
        }
