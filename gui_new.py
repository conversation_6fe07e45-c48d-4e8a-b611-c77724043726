"""
重构后的主GUI文件
看海量化交易系统 - 主界面
"""
import sys
import os
import ctypes
import subprocess
import logging
import time
from datetime import datetime

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QScrollArea, QMessageBox, QPushButton)
from PyQt5.QtCore import Qt, QTimer, QSettings, pyqtSignal
from PyQt5.QtGui import QIcon, QPalette, QColor, QPainter, QPen

# 导入自定义模块
from utils.config import ICON_PATH, MIN_WINDOW_SIZE
from utils.styles import MAIN_WINDOW_STYLE
from utils.helpers import (setup_logging, is_software_running, create_colored_pixmap, 
                          load_icon, calculate_window_geometry)
from components.settings_dialog import SettingsDialog
from components.splash_screen import CustomSplashScreen
from ui.toolbar import Toolbar
from ui.download_interface import DownloadInterface
from ui.cleaner_interface import CleanerInterface
from update_manager import UpdateManager
from version import get_version_info
from GUIplotLoadData import StockDataAnalyzerGUI
from khQTTools import download_and_store_data, supplement_history_data

class StockDataProcessorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # 检查QSettings存储位置
        settings = QSettings('KHQuant', 'StockAnalyzer')
        logging.info(f"QSettings存储位置: {settings.fileName()}")
        
        # 设置图标路径
        self.ICON_PATH = ICON_PATH
        logging.info(f"初始化图标路径: {self.ICON_PATH}")
        
        # 初始化组件
        self.visualization_window = None
        self.download_thread = None
        self.supplement_thread = None
        self.cleaner_thread = None
        self.update_thread = None
        
        # 初始化更新管理器
        self.initialize_update_manager()
        
        # 设置窗口属性
        self.setup_window_properties()
        
        # 获取版本信息
        self.version_info = get_version_info()
        
        # 初始化UI
        self.initUI()
        
        # 设置窗口拖拽和调整大小相关属性
        self.can_drag = False
        self.resizing = False
        self.resize_edge = None
        self.border_thickness = 20
        self.setMouseTracking(True)
        
        # 添加定时器来检查软件状态
        self.status_timer = QTimer(self)
        self.status_timer.timeout.connect(self.check_software_status)
        self.status_timer.start(5000)
        
        # 初始软件检查
        self.check_and_open_software()
        
        # 添加一个计时器用于延迟刷新
        self.refresh_timer = QTimer(self)
        self.refresh_timer.setSingleShot(True)
        self.refresh_timer.timeout.connect(self.refresh_folder)

        # 创建状态栏
        self.statusBar().showMessage('就绪')

    def setup_window_properties(self):
        """设置窗口属性"""
        self.setAttribute(Qt.WA_TranslucentBackground, False)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowSystemMenuHint)
        
        # 设置窗口背景色
        palette = self.palette()
        palette.setColor(QPalette.Window, QColor("#1E1E1E"))
        self.setPalette(palette)

    def initialize_update_manager(self):
        """初始化更新管理器"""
        self.update_manager = UpdateManager(self)
        self.update_manager.check_finished.connect(self.handle_update_check_finished)
        
        # 加载更新设置
        self.set_update_config()
        
    def set_update_config(self):
        """设置更新配置"""
        settings = QSettings('KHQuant', 'StockAnalyzer')
        self.update_manager.auto_check = settings.value('auto_check_update', True, type=bool)
        self.update_manager.update_channel = settings.value('update_channel', 'stable', type=str)

    def initUI(self):
        """初始化用户界面"""
        self.setWindowTitle('看海量化交易系统')
        self.setMinimumSize(MIN_WINDOW_SIZE, MIN_WINDOW_SIZE)
        
        # 计算并设置窗口几何尺寸
        x, y, width, height = calculate_window_geometry()
        self.setGeometry(x, y, width, height)
        
        # 设置窗口图标
        self.set_window_icon()
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # 添加自定义标题栏
        self.create_title_bar(main_layout)
        
        # 添加工具栏
        self.create_toolbar(main_layout)
        
        # 创建主要内容区域
        self.create_main_content(main_layout)
        
        # 应用样式
        self.setStyleSheet(MAIN_WINDOW_STYLE)

    def set_window_icon(self):
        """设置窗口图标"""
        icon_path = os.path.join(self.ICON_PATH, 'stock_icon.ico')
        if os.path.exists(icon_path):
            try:
                icon = QIcon(icon_path)
                if not icon.isNull():
                    self.setWindowIcon(icon)
                    logging.info("成功设置窗口图标")
                else:
                    logging.warning("QIcon返回了空图标")
            except Exception as e:
                logging.error(f"加载图标时出错: {str(e)}", exc_info=True)
        else:
            logging.warning(f"图标文件不存在: {icon_path}")

    def create_title_bar(self, main_layout):
        """创建自定义标题栏"""
        title_bar = QWidget()
        title_bar.setObjectName("titleBar")
        title_bar.setFixedHeight(60)
        title_bar_layout = QHBoxLayout(title_bar)
        title_bar_layout.setContentsMargins(10, 0, 10, 0)
        
        title_bar_layout.addStretch()
        title_label = QLabel("看海量化交易系统")
        title_label.setStyleSheet("color: #E0E0E0; font-weight: bold; font-size: 30px;")
        title_bar_layout.addWidget(title_label, alignment=Qt.AlignCenter)
        title_bar_layout.addStretch()

        # 添加帮助按钮
        help_button = QPushButton("?")
        help_button.setObjectName("helpButton")
        help_button.setFixedSize(40, 40)
        help_button.clicked.connect(self.show_help)
        title_bar_layout.addWidget(help_button)

        # 添加状态指示器
        self.status_indicator = QLabel()
        self.status_indicator.setFixedSize(20, 20)
        self.status_indicator.setToolTip("交易平台状态")
        title_bar_layout.addWidget(self.status_indicator)

        # 添加最小化、最大化和关闭按钮
        for button_text, func, obj_name in [("—", self.showMinimized, "minButton"), 
                                            ("□", self.toggle_maximize, "maxButton"), 
                                            ("×", self.close, "closeButton")]:
            button = QPushButton(button_text)
            button.setObjectName(obj_name)
            button.setFixedSize(40, 40)
            button.clicked.connect(func)
            title_bar_layout.addWidget(button)

        main_layout.addWidget(title_bar)

    def create_toolbar(self, main_layout):
        """创建工具栏"""
        self.toolbar = Toolbar(self.ICON_PATH, self)
        
        # 连接工具栏信号
        self.toolbar.visualization_requested.connect(self.open_visualization)
        self.toolbar.settings_requested.connect(self.show_settings)
        self.toolbar.update_check_requested.connect(self.check_for_updates)
        self.toolbar.version_info_requested.connect(self.show_current_version)
        
        main_layout.addWidget(self.toolbar)

    def create_main_content(self, main_layout):
        """创建主要内容区域"""
        # 创建水平布局来容纳两个界面
        h_layout = QHBoxLayout()

        # 添加左侧下载界面
        left_scroll = QScrollArea()
        left_scroll.setWidgetResizable(True)
        left_content = QWidget()
        left_scroll.setWidget(left_content)
        left_layout = QVBoxLayout(left_content)
        
        # 创建下载界面
        self.download_interface = DownloadInterface(self)
        self.download_interface.create_interface(left_layout)
        h_layout.addWidget(left_scroll)

        # 添加右侧清洗界面
        right_scroll = QScrollArea()
        right_scroll.setWidgetResizable(True)
        right_content = QWidget()
        right_scroll.setWidget(right_content)
        right_layout = QVBoxLayout(right_content)
        
        # 创建清洗界面
        self.cleaner_interface = CleanerInterface(self)
        self.cleaner_interface.create_interface(right_layout)
        h_layout.addWidget(right_scroll)

        # 将水平布局添加到主布局
        main_layout.addLayout(h_layout)

    def toggle_maximize(self):
        """切换最大化状态"""
        if self.isMaximized():
            self.showNormal()
            # 改变按钮文本为最大化图标
            for button in self.findChildren(QPushButton):
                if button.objectName() == "maxButton":
                    button.setText("□")
        else:
            self.showMaximized()
            # 改变按钮文本为恢复图标
            for button in self.findChildren(QPushButton):
                if button.objectName() == "maxButton":
                    button.setText("❐")

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            # 检查鼠标是否在标题栏内
            title_bar = self.findChild(QWidget, "titleBar")
            if title_bar and event.pos().y() <= title_bar.height():
                self.can_drag = True
                self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            else:
                # 检查是否在窗口边缘
                edge = self.get_resize_edge(event.pos())
                if edge:
                    self.resizing = True
                    self.resize_edge = edge
            event.accept()

    def paintEvent(self, event):
        """绘制事件"""
        # 添加自定义绘制以确保窗口边框正确显示
        painter = QPainter(self)
        painter.setPen(QColor("#3D3D3D"))
        painter.drawRect(self.rect().adjusted(0, 0, -1, -1))
        super().paintEvent(event)

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons() == Qt.LeftButton:
            if self.can_drag:
                self.move(event.globalPos() - self.drag_position)
            elif self.resizing:
                self.resize_window(event.globalPos())
        else:
            cursor_changed = False
            if event.pos().x() <= self.border_thickness:
                QApplication.setOverrideCursor(Qt.SizeHorCursor)
                cursor_changed = True
            elif event.pos().x() >= self.width() - self.border_thickness:
                QApplication.setOverrideCursor(Qt.SizeHorCursor)
                cursor_changed = True
            elif event.pos().y() <= self.border_thickness:
                QApplication.setOverrideCursor(Qt.SizeVerCursor)
                cursor_changed = True
            elif event.pos().y() >= self.height() - self.border_thickness:
                QApplication.setOverrideCursor(Qt.SizeVerCursor)
                cursor_changed = True

            if not cursor_changed:
                QApplication.restoreOverrideCursor()

        event.accept()

    def leaveEvent(self, event):
        """鼠标离开事件"""
        QApplication.restoreOverrideCursor()
        event.accept()

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        self.can_drag = False
        self.resizing = False
        self.resize_edge = None
        self.setCursor(Qt.ArrowCursor)
        event.accept()

    def get_resize_edge(self, pos):
        """获取调整大小的边缘"""
        rect = self.rect()
        if pos.x() <= self.border_thickness:
            if pos.y() <= self.border_thickness:
                return 'topleft'
            elif pos.y() >= rect.height() - self.border_thickness:
                return 'bottomleft'
            else:
                return 'left'
        elif pos.x() >= rect.width() - self.border_thickness:
            if pos.y() <= self.border_thickness:
                return 'topright'
            elif pos.y() >= rect.height() - self.border_thickness:
                return 'bottomright'
            else:
                return 'right'
        elif pos.y() <= self.border_thickness:
            return 'top'
        elif pos.y() >= rect.height() - self.border_thickness:
            return 'bottom'
        return None

    def resize_window(self, global_pos):
        """调整窗口大小"""
        new_geo = self.geometry()
        if self.resize_edge in ['left', 'topleft', 'bottomleft']:
            new_geo.setLeft(global_pos.x())
        if self.resize_edge in ['right', 'topright', 'bottomright']:
            new_geo.setRight(global_pos.x())
        if self.resize_edge in ['top', 'topleft', 'topright']:
            new_geo.setTop(global_pos.y())
        if self.resize_edge in ['bottom', 'bottomleft', 'bottomright']:
            new_geo.setBottom(global_pos.y())
        self.setGeometry(new_geo)

    def check_and_open_software(self):
        """检查并启动miniQMT软件"""
        try:
            settings = QSettings('KHQuant', 'StockAnalyzer')
            default_path = r"C:\国金证券QMT交易端\bin.x64\XtItClient.exe"
            software_path = settings.value('client_path', default_path)

            # 如果路径无效，只更新状态指示器，不显示警告
            if not os.path.exists(software_path):
                logging.warning(f"QMT客户端路径无效: {software_path}")
                self.update_status_indicator("red", "MiniQMT未启动")
                return False

            # 只有在路径有效且软件未运行时才尝试启动
            if not is_software_running("XtMiniQmt.exe"):
                try:
                    if ctypes.windll.shell32.IsUserAnAdmin() == 0:
                        ctypes.windll.shell32.ShellExecuteW(None, "runas", software_path, None, None, 1)
                    else:
                        subprocess.Popen(software_path)
                    return True
                except Exception as e:
                    logging.error(f"启动QMT客户端失败: {str(e)}")
                    self.update_status_indicator("red", "MiniQMT启动失败")
                    return False

            return True

        except Exception as e:
            logging.error(f"检查并启动软件时出错: {str(e)}")
            self.update_status_indicator("red", "MiniQMT状态检查失败")
            return False

    def update_status_indicator(self, color, tooltip, connection_status=None):
        """更新状态指示器的颜色和提示信息"""
        try:
            if hasattr(self, 'status_indicator'):
                # 获取当前MiniQMT状态
                miniQMT_status = "MiniQMT已启动" if is_software_running("XtMiniQmt.exe") else "MiniQMT未启动"

                # 确定显示颜色的逻辑
                if "未启动" in miniQMT_status:
                    display_color = "red"
                else:
                    display_color = "green"

                combined_tooltip = f"{miniQMT_status}\n{tooltip}"

                # 获取当前状态用于比较
                current_tooltip = self.status_indicator.toolTip()

                # 只在状态发生变化时更新并记录日志
                if current_tooltip != combined_tooltip:
                    self.status_indicator.setPixmap(create_colored_pixmap(display_color))
                    self.status_indicator.setToolTip(combined_tooltip)
                    logging.debug(f"状态指示器状态更新: {display_color} - {combined_tooltip}")

        except Exception as e:
            logging.error(f"更新状态指示器时出错: {str(e)}", exc_info=True)

    def check_software_status(self):
        """检查MiniQMT软件状态并更新指示器"""
        try:
            # 获取当前运行状态
            current_running = is_software_running("XtMiniQmt.exe")

            # 只在状态发生变化时更新
            if not hasattr(self, '_last_miniQMT_status') or self._last_miniQMT_status != current_running:
                self._last_miniQMT_status = current_running

                # 获取客户端路径
                settings = QSettings('KHQuant', 'StockAnalyzer')
                default_path = r"C:\国金证券QMT交易端\bin.x64\XtItClient.exe"
                software_path = settings.value('client_path', default_path)

                if not os.path.exists(software_path):
                    self.update_status_indicator("red", "MiniQMT未启动(路径无效)")
                else:
                    if current_running:
                        self.update_status_indicator("green", "MiniQMT已启动")
                    else:
                        self.update_status_indicator("red", "MiniQMT未启动")

        except Exception as e:
            logging.error(f"检查软件状态时出错: {str(e)}")
            self.update_status_indicator("red", "状态检查失败")

    def show_help(self):
        """显示帮助对话框"""
        # 这里可以添加帮助对话框的实现
        QMessageBox.information(self, "帮助", "看海量化交易系统\n\n功能说明：\n1. 数据下载：下载股票数据\n2. 数据清洗：清理和处理数据\n3. 数据可视化：查看数据图表")

    def show_settings(self):
        """显示设置对话框"""
        dialog = SettingsDialog(self)
        dialog.exec_()

    def show_current_version(self):
        """显示当前版本信息"""
        self.update_manager.show_current_version()

    def check_for_updates(self):
        """检查软件更新"""
        try:
            logging.info("开始检查软件更新")
            current_version = get_version_info()['version']
            self.update_manager.check_for_updates(current_version)
        except Exception as e:
            logging.error(f"检查更新时发生错误: {str(e)}", exc_info=True)
            QMessageBox.warning(self, "更新检查失败", f"检查更新时发生错误: {str(e)}")

    def handle_update_check_finished(self, success, message):
        """处理更新检查完成的回调"""
        logging.info(f"更新检查完成: 成功={success}, 消息={message}")

    def open_visualization(self):
        """打开数据可视化窗口"""
        if not hasattr(self, 'visualization_window') or not self.visualization_window:
            self.visualization_window = StockDataAnalyzerGUI()
            # 如果已经选择了数据文件夹，自动传递给可视化窗口
            if hasattr(self.download_interface, 'local_data_path_edit') and self.download_interface.local_data_path_edit.text():
                self.visualization_window.folder_path_label.setText(self.download_interface.local_data_path_edit.text())
                self.visualization_window.analyze_folder(self.download_interface.local_data_path_edit.text())

        self.visualization_window.show()
        self.visualization_window.raise_()
        self.visualization_window.activateWindow()

    def refresh_folder(self):
        """刷新当前加载的文件夹内容"""
        current_path = self.download_interface.local_data_path_edit.text().strip()
        if current_path and os.path.exists(current_path):
            self.cleaner_interface.load_folder_info(current_path)
            logging.info(f"已刷新文件夹内容: {current_path}")

    def update_folder_path_label(self, text):
        """更新文件夹路径标签的文本"""
        if hasattr(self.cleaner_interface, 'folder_path_label'):
            self.cleaner_interface.folder_path_label.setText(text)

    def open_custom_list(self, event):
        """打开自选清单文件"""
        try:
            # 获取自选清单文件路径
            data_dir = os.path.join(os.path.dirname(__file__), 'data')
            custom_file = os.path.join(data_dir, 'otheridx.csv')

            if os.path.exists(custom_file):
                if sys.platform == 'win32':
                    os.startfile(custom_file)
                else:
                    import subprocess
                    subprocess.call(['xdg-open', custom_file])
                logging.info(f"已打开自选清单文件: {custom_file}")
            else:
                # 如果文件不存在，创建一个空的自选清单文件
                try:
                    os.makedirs(data_dir, exist_ok=True)
                    with open(custom_file, 'w', encoding='utf-8') as f:
                        f.write("code,name\n")  # 写入表头
                    if sys.platform == 'win32':
                        os.startfile(custom_file)
                    else:
                        import subprocess
                        subprocess.call(['xdg-open', custom_file])
                    logging.info(f"已创建并打开新的自选清单文件: {custom_file}")
                except Exception as e:
                    QMessageBox.warning(self, "错误", f"创建自选清单文件失败: {str(e)}")
                    logging.error(f"创建自选清单文件失败: {str(e)}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"打开自选清单文件失败: {str(e)}")
            logging.error(f"打开自选清单文件失败: {str(e)}")

    def update_status(self, message):
        """更新状态信息"""
        try:
            # 在状态栏显示消息
            self.statusBar().showMessage(message)
            # 确保消息立即显示
            QApplication.processEvents()
            # 记录日志
            logging.info(message)
        except Exception as e:
            logging.error(f"更新状态信息时出错: {str(e)}")

    def update_progress(self, value):
        """更新进度条"""
        if hasattr(self.download_interface, 'progress_bar'):
            self.download_interface.progress_bar.setValue(value)

    def download_data(self):
        """下载数据"""
        try:
            logging.info("开始准备下载数据")

            # 验证日期和时间范围
            if not self.validate_date_range():
                return
            if not self.validate_time_range():
                return

            # 清理之前的线程（如果存在）
            if hasattr(self, 'download_thread') and self.download_thread:
                try:
                    self.download_thread.stop()
                    self.download_thread.wait()
                except:
                    pass

            selected_fields = [field for field, checkbox in self.download_interface.field_checkboxes.items() if checkbox.isChecked()]
            if not selected_fields:
                QMessageBox.warning(self, "警告", "请至少选择一个字段")
                return

            # 获取周期类型和复权方式
            period_type = self.download_interface.period_type_combo.currentText()
            dividend_type = self.download_interface.dividend_type_combo.currentData()
            if dividend_type is None:  # 如果没有设置currentData，则使用currentText
                dividend_type = self.download_interface.dividend_type_combo.currentText()

            # 参数验证
            local_data_path = self.download_interface.local_data_path_edit.text().strip()
            if not local_data_path:
                QMessageBox.warning(self, "警告", "请设置数据存储路径")
                return

            # 获取选中的股票列表文件
            current_preview = self.download_interface.stock_files_preview.toPlainText().strip()
            if not current_preview or current_preview == "未选择任何股票列表文件":
                QMessageBox.warning(self, "警告", "请选择至少一个股票列表")
                return

            # 获取所有选中的文件路径
            selected_files = [f.strip() for f in current_preview.split('\n') if f.strip()]

            # 检查文件是否存在
            for file in selected_files:
                if not os.path.exists(file):
                    QMessageBox.warning(self, "警告", f"文件不存在: {file}")
                    return

            # 准备下载参数
            try:
                download_and_store_data(
                    local_data_path=local_data_path,
                    stock_files=selected_files,
                    field_list=selected_fields,
                    period_type=period_type,
                    start_date=self.download_interface.start_date_edit.date().toString('yyyyMMdd'),
                    end_date=self.download_interface.end_date_edit.date().toString('yyyyMMdd'),
                    dividend_type=dividend_type,
                    progress_callback=self.update_progress,
                    log_callback=self.update_status
                )
                QMessageBox.information(self, "完成", "数据下载完成！")
            except Exception as e:
                logging.error(f"下载数据时出错: {str(e)}")
                QMessageBox.critical(self, "错误", f"下载数据时出错: {str(e)}")

        except Exception as e:
            logging.error(f"准备下载数据时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"准备下载数据时出错: {str(e)}")

    def supplement_data(self):
        """补充数据"""
        try:
            # 验证日期和时间范围
            if not self.validate_date_range():
                return
            if not self.validate_time_range():
                return

            # 获取选中的股票文件列表
            stock_files = []
            current_preview = self.download_interface.stock_files_preview.toPlainText().strip()
            if current_preview and current_preview != "未选择任何股票列表文件":
                stock_files = current_preview.split('\n')

            if not stock_files:
                QMessageBox.warning(self, "警告", "请先选择股票列表文件！")
                return

            # 获取选中的字段列表
            field_list = []
            for field, checkbox in self.download_interface.field_checkboxes.items():
                if checkbox.isChecked():
                    field_list.append(field)

            if not field_list:
                QMessageBox.warning(self, "警告", "请至少选择一个数据字段！")
                return

            # 获取周期类型
            period_type = self.download_interface.period_type_combo.currentText()

            # 获取日期范围
            start_date = self.download_interface.start_date_edit.date().toString("yyyyMMdd")
            end_date = self.download_interface.end_date_edit.date().toString("yyyyMMdd")

            # 获取时间范围
            time_range = 'all'
            if self.download_interface.use_all_time_checkbox.currentIndex() == 0:  # 指定时间段
                start_time = self.download_interface.start_time_edit.time().toString("HH:mm")
                end_time = self.download_interface.end_time_edit.time().toString("HH:mm")
                time_range = f"{start_time}-{end_time}"

            # 更新按钮状态
            self.download_interface.supplement_button.setEnabled(False)
            self.download_interface.download_button.setEnabled(False)
            self.download_interface.progress_bar.setValue(0)

            try:
                # 调用补充数据函数，不传递复权参数
                supplement_history_data(
                    stock_files=stock_files,
                    field_list=field_list,
                    period_type=period_type,
                    start_date=start_date,
                    end_date=end_date,
                    time_range=time_range,
                    progress_callback=self.update_progress,
                    log_callback=self.update_status
                )

                QMessageBox.information(self, "完成", "数据补充完成！")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"补充数据时出错：{str(e)}")
                logging.error(f"补充数据时出错: {str(e)}", exc_info=True)

            finally:
                # 恢复按钮状态
                self.download_interface.supplement_button.setEnabled(True)
                self.download_interface.download_button.setEnabled(True)
                self.download_interface.progress_bar.setValue(0)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"准备补充数据时出错：{str(e)}")
            logging.error(f"准备补充数据时出错: {str(e)}", exc_info=True)

    def validate_date_range(self):
        """验证日期范围"""
        start_date = self.download_interface.start_date_edit.date()
        end_date = self.download_interface.end_date_edit.date()

        if end_date < start_date:
            QMessageBox.warning(self, "警告", "结束日期不能早于开始日期")
            return False

        return True

    def validate_time_range(self):
        """验证时间范围"""
        # 只在选择"指定时间段"时进行验证
        if self.download_interface.use_all_time_checkbox.currentIndex() == 0:
            start_time = self.download_interface.start_time_edit.time()
            end_time = self.download_interface.end_time_edit.time()

            if start_time >= end_time:
                QMessageBox.warning(self, "警告", "开始时间必须早于结束时间")
                return False

        return True

    def show(self):
        """重写show方法"""
        try:
            super().show()

            # 延迟检查更新
            QTimer.singleShot(2000, self.delayed_update_check)

        except Exception as e:
            logging.error(f"显示主窗口时出错: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"启动程序时出错: {str(e)}")
            QApplication.quit()

    def delayed_update_check(self):
        """延迟执行更新检查"""
        try:
            self.check_for_updates()
        except Exception as e:
            logging.error(f"延迟更新检查时出错: {str(e)}", exc_info=True)

    def closeEvent(self, event):
        """窗口关闭时的处理"""
        try:
            # 停止所有定时器
            if hasattr(self, 'status_timer'):
                self.status_timer.stop()
            if hasattr(self, 'refresh_timer'):
                self.refresh_timer.stop()

            # 停止下载线程
            if hasattr(self, 'download_thread') and self.download_thread:
                logging.info("正在停止下载线程...")
                self.download_thread.stop()
                self.download_thread.wait()
                self.download_thread = None
                logging.info("下载线程已停止")

            # 停止补充数据线程
            if hasattr(self, 'supplement_thread') and self.supplement_thread:
                logging.info("正在停止补充数据线程...")
                self.supplement_thread.stop()
                self.supplement_thread.wait()
                self.supplement_thread = None
                logging.info("补充数据线程已停止")

            # 停止清洗线程
            if hasattr(self, 'cleaner_thread') and self.cleaner_thread:
                logging.info("正在停止清洗线程...")
                self.cleaner_thread.terminate()
                self.cleaner_thread.wait()
                self.cleaner_thread = None
                logging.info("清洗线程已停止")

            # 停止更新线程
            if hasattr(self, 'update_thread') and self.update_thread:
                logging.info("正在停止更新线程...")
                self.update_thread.stop()
                self.update_thread.wait()
                self.update_thread = None
                logging.info("更新线程已停止")

            # 关闭可视化窗口
            if hasattr(self, 'visualization_window') and self.visualization_window:
                logging.info("正在关闭可视化窗口...")
                self.visualization_window.close()
                self.visualization_window = None
                logging.info("可视化窗口已关闭")

            logging.info("程序正常退出")
            event.accept()

        except Exception as e:
            logging.error(f"程序退出时出错: {str(e)}", exc_info=True)
            event.accept()  # 确保程序能够退出


def main():
    """主程序入口"""
    # 日志配置部分
    log_filename = setup_logging()

    try:
        logging.info("程序启动")
        app = QApplication(sys.argv)

        # 设置应用程序图标
        icon_file = os.path.join(ICON_PATH, 'stock_icon.ico')
        if os.path.exists(icon_file):
            app_icon = QIcon(icon_file)
            app.setWindowIcon(app_icon)
            logging.info("成功加载应用图标")
        else:
            logging.warning(f"图标文件不存在: {icon_file}")

        # 创建主窗口但不显示
        main_window = None
        try:
            # 创建并显示启动画面
            splash = None
            try:
                splash_img = os.path.join(ICON_PATH, 'splash.png')
                if os.path.exists(splash_img):
                    splash = CustomSplashScreen()
                    splash.show()
                    app.processEvents()
                    logging.info("启动画面显示成功")
                else:
                    logging.warning("未找到启动画面图片，跳过启动画面显示")
            except Exception as splash_error:
                logging.error(f"显示启动画面时出错: {str(splash_error)}")
                splash = None

            # 创建主窗口
            main_window = StockDataProcessorGUI()
            logging.info("主窗口创建成功")

            if splash:
                # 模拟加载过程
                loading_steps = [
                    (20, "正在初始化系统..."),
                    (40, "正在检查更新..."),
                    (60, "正在加载组件..."),
                    (80, "正在准备用户界面..."),
                    (100, "启动完成")
                ]

                for progress, message in loading_steps:
                    logging.info(f"加载进度: {progress}% - {message}")
                    splash.set_progress(progress, message)
                    app.processEvents()
                    time.sleep(0.05)

                # 关闭启动画面
                splash.close()
                logging.info("启动画面已关闭")
                app.processEvents()

            def show_main_window():
                """显示主窗口的辅助函数"""
                try:
                    main_window.show()
                    main_window.raise_()
                    main_window.activateWindow()
                except Exception as e:
                    logging.error(f"显示主窗口时出错: {str(e)}", exc_info=True)
                    QMessageBox.critical(None, "错误", f"显示主窗口时出错: {str(e)}")
                    QApplication.quit()

            # 使用短延时确保启动画面完全关闭后再显示主窗口
            QTimer.singleShot(100, show_main_window)

            # 设置定时检查
            status_timer = QTimer()
            status_timer.timeout.connect(main_window.check_software_status)
            status_timer.start(5000)

            # 延迟执行其他初始化操作
            QTimer.singleShot(200, main_window.check_software_status)
            QTimer.singleShot(2000, main_window.delayed_update_check)

            logging.info("开始事件循环")
            return_code = app.exec_()
            logging.info(f"程序退出，返回码: {return_code}")
            sys.exit(return_code)

        except Exception as e:
            logging.error(f"初始化过程中出错: {str(e)}", exc_info=True)
            if main_window:
                main_window.close()
            if splash:
                splash.close()
            QMessageBox.critical(None, "初始化错误",
                               f"程序初始化过程中出错:\n{str(e)}\n\n详细信息已写入日志文件")
            sys.exit(1)

    except Exception as e:
        logging.critical(f"程序异常退出: {str(e)}", exc_info=True)
        print(f"程序发生严重错误，详细信息已写入日志文件: {log_filename}")
        sys.exit(1)


if __name__ == '__main__':
    main()
